#!/usr/bin/env python3
"""
Build script for creating a standalone wake word detector executable
"""
import os
import sys
import subprocess
import shutil
import platform

def main():
    print("🔨 Building TeamBye Wake Word Detector...")
    
    # Create venv if not exists
    venv_path = "openwakeword_venv"
    if not os.path.exists(venv_path):
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
    
    # Determine pip path based on OS
    if platform.system() == "Windows":
        pip_path = os.path.join(venv_path, "Scripts", "pip")
        python_path = os.path.join(venv_path, "Scripts", "python")
    else:
        pip_path = os.path.join(venv_path, "bin", "pip")
        python_path = os.path.join(venv_path, "bin", "python")
    
    # Install requirements
    print("📚 Installing requirements...")
    subprocess.run([pip_path, "install", "-r", "openwakeword/requirements.txt"], check=True)
    subprocess.run([pip_path, "install", "pyinstaller"], check=True)

    # Download required openWakeWord models
    print("📥 Downloading openWakeWord models...")
    subprocess.run([python_path, "-c", """
import openwakeword
openwakeword.utils.download_models()
"""], check=True)
    
    # Create modified detector script with correct model path
    print("✏️ Creating production detector script...")
    
    # Read the original script and modify the model path
    with open("openwakeword/team_bye_detector.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Replace hardcoded path with relative path
    modified_content = content.replace(
        'default="/home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite"',
        'default="team_bye.tflite"'
    ).replace(
        'model_path: str = "/home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite"',
        'model_path: str = "team_bye.tflite"'
    )
    
    with open("openwakeword/team_bye_detector_prod.py", "w", encoding="utf-8") as f:
        f.write(modified_content)
    
    # Get the path to openWakeWord models
    import site
    site_packages = site.getsitepackages()[0]
    oww_models_path = os.path.join(site_packages, "openwakeword", "resources", "models")

    # Create PyInstaller spec file
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['openwakeword/team_bye_detector_prod.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('openwakeword/team_bye.tflite', '.'),
        ('{oww_models_path}', 'openwakeword/resources/models'),
    ],
    hiddenimports=['numpy', 'scipy._lib.messagestream'],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='team_bye_detector',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("team_bye_detector.spec", "w") as f:
        f.write(spec_content)
    
    # Build with PyInstaller
    print("🏗️ Building executable...")
    pyinstaller_path = os.path.join(venv_path, "Scripts" if platform.system() == "Windows" else "bin", "pyinstaller")
    subprocess.run([pyinstaller_path, "team_bye_detector.spec", "--clean"], check=True)
    
    # Move executable to src-tauri/binaries
    os.makedirs("src-tauri/binaries", exist_ok=True)
    
    exe_name = "team_bye_detector.exe" if platform.system() == "Windows" else "team_bye_detector"
    src_path = os.path.join("dist", exe_name)
    
    if platform.system() == "Linux":
        dest_path = "src-tauri/binaries/team_bye_detector-x86_64-unknown-linux-gnu"
    elif platform.system() == "Darwin":
        dest_path = "src-tauri/binaries/team_bye_detector-x86_64-apple-darwin"
    else:  # Windows
        dest_path = "src-tauri/binaries/team_bye_detector-x86_64-pc-windows-msvc.exe"
    
    shutil.copy2(src_path, dest_path)
    
    print("✅ Build completed successfully!")
    print(f"📁 Executable location: {dest_path}")
    
    # Clean up temp files
    if os.path.exists("team_bye_detector.spec"):
        os.remove("team_bye_detector.spec")
    if os.path.exists("openwakeword/team_bye_detector_prod.py"):
        os.remove("openwakeword/team_bye_detector_prod.py")

if __name__ == "__main__":
    main()