#!/usr/bin/env python3
"""
تشخیص کلمه "Team Bye" با استفاده از openWakeWord
مدل: team_bye.tflite
مسیر مدل: /home/<USER>/Desktop/main/projects/openWakeWord/team_bye.tflite
"""

import argparse
import datetime
import logging
import signal
import sys
import time
import os
from typing import Dict, Optional, List

import numpy as np
import pyaudio
from openwakeword.model import Model


class TeamByeDetector:
    """کلاس اصلی تشخیص کلمه Team Bye"""
    
    def __init__(self, 
                 model_path: str = "team_bye.tflite",
                 chunk_size: int = 1280,
                 threshold: float = 0.5, 
                 sample_rate: int = 16000,
                 channels: int = 1,
                 device_id: Optional[int] = None,
                 cooldown_time: float = 2.0,
                 smoothing_window: int = 3):
        """
        مقداردهی تشخیص‌گر Team Bye
        
        Args:
            model_path: مسیر فایل مدل openWakeWord (.tflite)
            chunk_size: اندازه فریم صوتی (80ms = 1280 نمونه در 16kHz)
            threshold: آستانه تشخیص (0.0-1.0)
            sample_rate: نرخ نمونه‌برداری (باید 16000 باشد)
            channels: تعداد کانال‌ها (باید 1 باشد)
            device_id: شناسه دستگاه میکروفون (None = پیش‌فرض)
            cooldown_time: زمان انتظار بعد از هر تشخیص (ثانیه)
            smoothing_window: اندازه پنجره هموارسازی
        """
        self.model_path = model_path
        self.chunk_size = chunk_size
        self.threshold = threshold
        self.sample_rate = sample_rate
        self.channels = channels
        self.device_id = device_id
        self.cooldown_time = cooldown_time
        self.smoothing_window = smoothing_window
        
        # متغیرهای داخلی
        self.audio = None
        self.mic_stream = None
        self.oww_model = None
        self.model_name = None
        self.last_detection_time = 0
        self.recent_scores = []
        self.is_running = False
        
        # تنظیم logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def list_audio_devices(self):
        """فهرست دستگاه‌های صوتی موجود"""
        audio = pyaudio.PyAudio()
        print("\n=== دستگاه‌های صوتی موجود ===")
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:  # فقط دستگاه‌های ورودی
                print(f"ID {i}: {info['name']} - {info['maxInputChannels']} کانال - {info['defaultSampleRate']} Hz")
        audio.terminate()
        print("=====================================\n")

    def initialize_audio(self):
        """مقداردهی سیستم صوتی"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # بررسی دستگاه انتخاب شده
            if self.device_id is not None:
                device_info = self.audio.get_device_info_by_index(self.device_id)
                self.logger.info(f"استفاده از دستگاه: {device_info['name']}")
            
            # باز کردن جریان میکروفون
            self.mic_stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_id,
                frames_per_buffer=self.chunk_size
            )
            
            self.logger.info(f"جریان صوتی باز شد: {self.sample_rate}Hz، {self.chunk_size} فریم")
            return True
            
        except Exception as e:
            self.logger.error(f"خطا در مقداردهی صوت: {e}")
            return False

    def initialize_model(self):
        """بارگذاری مدل openWakeWord"""
        try:
            # بررسی وجود فایل مدل
            if not os.path.exists(self.model_path):
                self.logger.error(f"فایل مدل یافت نشد: {self.model_path}")
                return False
            
            # بارگذاری مدل با openWakeWord
            self.oww_model = Model(
                wakeword_models=[self.model_path],
                inference_framework='tflite'
            )
            
            # استخراج نام مدل از مسیر فایل
            filename = os.path.basename(self.model_path)
            self.model_name = filename.replace('.tflite', '')
            
            # بررسی مدل‌های بارگذاری شده
            available_models = list(self.oww_model.models.keys())
            self.logger.info(f"مدل بارگذاری شد: {self.model_path}")
            self.logger.info(f"مدل‌های موجود: {available_models}")
            
            # اگر نام مدل در مدل‌های موجود نیست، از اولین مدل استفاده کن
            if self.model_name not in available_models:
                if available_models:
                    self.model_name = available_models[0]
                    self.logger.info(f"استفاده از مدل: {self.model_name}")
                else:
                    raise ValueError("هیچ مدلی بارگذاری نشد")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطا در بارگذاری مدل: {e}")
            return False

    def smooth_score(self, score: float) -> float:
        """هموارسازی نمره‌ها برای کاهش نویز"""
        self.recent_scores.append(score)
        
        # نگهداشتن فقط چند نمره اخیر
        if len(self.recent_scores) > self.smoothing_window:
            self.recent_scores.pop(0)
        
        # محاسبه میانگین موزون (وزن بیشتر به نمره‌های جدیدتر)
        weights = np.linspace(0.5, 1.0, len(self.recent_scores))
        weighted_avg = np.average(self.recent_scores, weights=weights)
        
        return weighted_avg

    def is_in_cooldown(self) -> bool:
        """بررسی وضعیت cooldown"""
        current_time = time.time()
        return (current_time - self.last_detection_time) < self.cooldown_time

    def detect_team_bye(self) -> Optional[Dict]:
        """تشخیص کلمه Team Bye در یک فریم"""
        try:
            # دریافت داده صوتی
            audio_data = self.mic_stream.read(
                self.chunk_size, 
                exception_on_overflow=False
            )
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # پیش‌بینی با مدل openWakeWord
            predictions = self.oww_model.predict(audio_array)
            
            # دریافت نمره مدل هدف
            raw_score = predictions.get(self.model_name, 0.0)
            
            # هموارسازی نمره
            smooth_score = self.smooth_score(raw_score)
            
            # بررسی آستانه و cooldown
            if smooth_score >= self.threshold and not self.is_in_cooldown():
                self.last_detection_time = time.time()
                return {
                    'model': self.model_name,
                    'raw_score': raw_score,
                    'smooth_score': smooth_score,
                    'threshold': self.threshold,
                    'timestamp': datetime.datetime.now()
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطا در تشخیص: {e}")
            return None

    def print_detection_log(self, detection: Dict):
        """چاپ لاگ تشخیص"""
        timestamp = detection['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        model = detection['model']
        raw_score = detection['raw_score']
        smooth_score = detection['smooth_score']
        threshold = detection['threshold']
        
        # چاپ با رنگ (اگر ترمینال پشتیبانی کند)
        try:
            print(f"\033[95m👋 [{timestamp}] '{model.upper()}' تشخیص داده شد!\033[0m")
            print(f"   📊 نمره خام: {raw_score:.4f}")
            print(f"   📈 نمره هموار: {smooth_score:.4f}")
            print(f"   🎯 آستانه: {threshold}")
            print(f"   {'='*60}")
        except:
            # fallback برای ترمینال‌هایی که رنگ را پشتیبانی نمی‌کنند
            print(f"[{timestamp}] '{model.upper()}' تشخیص داده شد!")
            print(f"   نمره خام: {raw_score:.4f}, نمره هموار: {smooth_score:.4f}, آستانه: {threshold}")

    def signal_handler(self, signum, frame):
        """مدیریت سیگنال‌های سیستم (Ctrl+C)"""
        self.logger.info("\nدریافت سیگنال خروج...")
        self.is_running = False

    def cleanup(self):
        """پاک‌سازی منابع"""
        if self.mic_stream:
            self.mic_stream.stop_stream()
            self.mic_stream.close()
            self.logger.info("جریان میکروفون بسته شد")
        
        if self.audio:
            self.audio.terminate()
            self.logger.info("سیستم صوتی بسته شد")

    def run(self):
        """اجرای حلقه اصلی تشخیص"""
        # تنظیم signal handler
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # مقداردهی سیستم‌ها
        if not self.initialize_audio():
            return False
        
        if not self.initialize_model():
            self.cleanup()
            return False
        
        # شروع تشخیص
        self.is_running = True
        self.logger.info("=" * 80)
        self.logger.info("👋 شروع تشخیص کلمه 'TEAM BYE'")
        self.logger.info(f"📁 مسیر مدل: {self.model_path}")
        self.logger.info(f"🎯 آستانه: {self.threshold}")
        self.logger.info(f"⏱️  زمان Cooldown: {self.cooldown_time} ثانیه")
        self.logger.info(f"📊 پنجره هموارسازی: {self.smoothing_window}")
        self.logger.info("   برای خروج Ctrl+C را فشار دهید")
        self.logger.info("=" * 80)
        
        try:
            while self.is_running:
                detection = self.detect_team_bye()
                if detection:
                    self.print_detection_log(detection)
                
                # استراحت کوتاه برای جلوگیری از استفاده بالای CPU
                time.sleep(0.01)
                
        except KeyboardInterrupt:
            self.logger.info("\nخروج با Ctrl+C...")
        except Exception as e:
            self.logger.error(f"خطای غیرمنتظره: {e}")
        finally:
            self.cleanup()
            self.logger.info("برنامه به پایان رسید")
        
        return True


def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(
        description="تشخیص کلمه Team Bye با TensorFlow Lite",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--model-path", "-m",
        type=str,
        default="team_bye.tflite",
        help="مسیر فایل مدل TensorFlow Lite"
    )
    
    parser.add_argument(
        "--threshold", "-t",
        type=float,
        default=0.5,
        help="آستانه تشخیص (0.0-1.0، پایین‌تر = حساسیت بیشتر)"
    )
    
    parser.add_argument(
        "--device-id", "-d",
        type=int,
        default=None,
        help="شناسه دستگاه میکروفون (برای مشاهده فهرست: --list-devices)"
    )
    
    parser.add_argument(
        "--list-devices", "-l",
        action="store_true",
        help="نمایش فهرست دستگاه‌های صوتی"
    )
    
    parser.add_argument(
        "--chunk-size", "-c",
        type=int,
        default=1280,
        help="اندازه فریم صوتی (نمونه، 1280 = 80ms در 16kHz)"
    )
    
    parser.add_argument(
        "--cooldown", "-cd",
        type=float,
        default=2.0,
        help="زمان انتظار بعد از هر تشخیص (ثانیه)"
    )
    
    parser.add_argument(
        "--smoothing", "-s",
        type=int,
        default=3,
        help="اندازه پنجره هموارسازی نمره‌ها"
    )
    
    args = parser.parse_args()
    
    # ایجاد instance تشخیص‌گر
    detector = TeamByeDetector(
        model_path=args.model_path,
        chunk_size=args.chunk_size,
        threshold=args.threshold,
        device_id=args.device_id,
        cooldown_time=args.cooldown,
        smoothing_window=args.smoothing
    )
    
    # نمایش فهرست دستگاه‌ها در صورت درخواست
    if args.list_devices:
        detector.list_audio_devices()
        return
    
    # اجرای تشخیص‌گر
    success = detector.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()