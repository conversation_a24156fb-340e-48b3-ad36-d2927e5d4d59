#!/bin/bash

echo "🚀 Setting up Wake Word Detection for TeamBy Desktop"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    exit 1
fi

print_status "Python 3 found: $(python3 --version)"

# Check if we're in the right directory
if [ ! -f "openwakeword/team_bye_detector.py" ]; then
    print_error "Must be run from the TeamBy desktop root directory"
    exit 1
fi

# Step 1: Build the wake word detector
print_status "Building wake word detector..."
if python3 build_wake_word.py; then
    print_status "Wake word detector built successfully"
else
    print_error "Failed to build wake word detector"
    exit 1
fi

# Step 2: Check if binary was created
BINARY_PATH=""
if [ "$(uname)" = "Linux" ]; then
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-unknown-linux-gnu"
elif [ "$(uname)" = "Darwin" ]; then
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-apple-darwin"
else
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-pc-windows-msvc.exe"
fi

if [ -f "$BINARY_PATH" ]; then
    print_status "Binary created: $BINARY_PATH"
    
    # Make sure it's executable
    chmod +x "$BINARY_PATH"
    print_status "Made binary executable"
else
    print_error "Binary not found at expected location: $BINARY_PATH"
    exit 1
fi

# Step 3: Test the binary (optional quick test)
print_warning "Testing binary (will exit after 3 seconds)..."
timeout 3s "$BINARY_PATH" --help > /dev/null 2>&1
if [ $? -eq 124 ]; then
    print_status "Binary appears to be working (timed out as expected)"
elif [ $? -eq 0 ]; then
    print_status "Binary help command works"
else
    print_warning "Binary test had issues, but proceeding..."
fi

# Step 4: Clean up build artifacts
print_status "Cleaning up build artifacts..."
rm -rf build/ dist/ *.spec openwakeword_venv/
print_status "Cleanup completed"

echo ""
echo "🎉 Wake Word Detection setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Run 'npm run tauri:dev' to test in development"
echo "2. Use the WakeWordTest component to test detection"
echo "3. Say 'Team Bye' to test detection"
echo ""
echo "For production build:"
echo "- Run 'npm run tauri:build' to create distributable app"
echo ""

# Display some useful info
echo "📋 Configuration:"
echo "   Model file: openwakeword/team_bye.tflite"
echo "   Binary: $BINARY_PATH"
echo "   Default threshold: 0.4"
echo "   Default cooldown: 2.0 seconds"
echo ""