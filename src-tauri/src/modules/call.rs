use tauri::A<PERSON><PERSON><PERSON><PERSON>;
use std::time::Duration;

use super::utils::{get_auth_token, create_http_client, log_api_request, log_api_response};
// Note: CallRequest and CallResponse types are available but not used directly in this implementation
// They can be used for future type-safe request/response handling

// Make call API request
#[tauri::command]
pub async fn make_call_request(app: AppHandle, target_employee_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Making call request to employee ID: {}", target_employee_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "target_employee_id": target_employee_id,
        "meet_type": "audio"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/";
    log_api_request("POST", url, "CALL");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL");

    match status.as_u16() {
        201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(call_response) => {
                    println!("✅ [CALL] Call request successful");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": 201,
                        "data": call_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse call response: {}", e);
                    println!("❌ [CALL] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Call request failed with status: {}", status);
            println!("❌ [CALL] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}

// Answer call API request
#[tauri::command]
pub async fn answer_call_request(app: AppHandle, call_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Answering call ID: {}", call_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "call_id": call_id,
        "action": "answer"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/answer/";
    log_api_request("POST", url, "CALL_ANSWER");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL_ANSWER] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL_ANSWER");

    match status.as_u16() {
        200 | 201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(answer_response) => {
                    println!("✅ [CALL_ANSWER] Call answered successfully");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": status.as_u16(),
                        "data": answer_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse answer response: {}", e);
                    println!("❌ [CALL_ANSWER] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Answer call failed with status: {}", status);
            println!("❌ [CALL_ANSWER] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}

// Reject call API request
#[tauri::command]
pub async fn reject_call_request(app: AppHandle, call_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Rejecting call ID: {}", call_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "call_id": call_id,
        "action": "reject"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/reject/";
    log_api_request("POST", url, "CALL_REJECT");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL_REJECT] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL_REJECT");

    match status.as_u16() {
        200 | 201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(reject_response) => {
                    println!("✅ [CALL_REJECT] Call rejected successfully");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": status.as_u16(),
                        "data": reject_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse reject response: {}", e);
                    println!("❌ [CALL_REJECT] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Reject call failed with status: {}", status);
            println!("❌ [CALL_REJECT] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}

// Cancel call API request (caller declines)
#[tauri::command]
pub async fn cancel_call_request(app: AppHandle, meet_id: u32, target_employee_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Canceling call - meet_id: {}, target_employee_id: {}", meet_id, target_employee_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "meet_id": meet_id,
        "action": "decline",
        "target_employee_id": target_employee_id
    });

    let url = "http://127.0.0.1:8000/api/meet/call/response/";
    log_api_request("POST", url, "CALL_CANCEL");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL_CANCEL] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL_CANCEL");

    match status.as_u16() {
        200 | 201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(cancel_response) => {
                    println!("✅ [CALL_CANCEL] Call canceled successfully");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": status.as_u16(),
                        "data": cancel_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse cancel response: {}", e);
                    println!("❌ [CALL_CANCEL] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Cancel call failed with status: {}", status);
            println!("❌ [CALL_CANCEL] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}

// End call API request
#[tauri::command]
pub async fn end_call_request(app: AppHandle, call_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Ending call ID: {}", call_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "call_id": call_id,
        "action": "end"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/end/";
    log_api_request("POST", url, "CALL_END");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL_END] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL_END");

    match status.as_u16() {
        200 | 201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(end_response) => {
                    println!("✅ [CALL_END] Call ended successfully");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": status.as_u16(),
                        "data": end_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse end response: {}", e);
                    println!("❌ [CALL_END] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("End call failed with status: {}", status);
            println!("❌ [CALL_END] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}

// Accept call API request (using /api/meet/call/response/ endpoint)
#[tauri::command]
pub async fn accept_call_request(app: AppHandle, meet_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Accepting call with meet_id: {}", meet_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "meet_id": meet_id,
        "action": "accept"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/response/";
    log_api_request("POST", url, "CALL_ACCEPT");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("X-Api-Key", &token)
        .header("version", super::types::VERSION.to_string())
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL_ACCEPT] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL_ACCEPT");

    match status.as_u16() {
        200 | 201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(accept_response) => {
                    println!("✅ [CALL_ACCEPT] Call accepted successfully");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": status.as_u16(),
                        "data": accept_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse accept response: {}", e);
                    println!("❌ [CALL_ACCEPT] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Accept call failed with status: {}", status);
            println!("❌ [CALL_ACCEPT] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}