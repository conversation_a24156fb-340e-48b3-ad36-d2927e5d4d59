use std::sync::{<PERSON>, Mutex};
use tauri::{State, AppHandle, Emitter};
use tauri_plugin_shell::ShellExt;
use log::{info, error, warn};
use serde_json;

// State for managing the wake word detector process
#[derive(Debug)]
pub struct WakeWordState {
    pub is_running: Arc<Mutex<bool>>,
}

impl WakeWordState {
    pub fn new() -> Self {
        WakeWordState {
            is_running: Arc::new(Mutex::new(false)),
        }
    }
}

#[tauri::command]
pub async fn start_wake_word_detection(
    state: State<'_, WakeWordState>,
    app_handle: tauri::AppHandle,
) -> Result<String, String> {
    let mut is_running = state.is_running.lock().unwrap();
    
    if *is_running {
        return Ok("Wake word detection is already running".to_string());
    }

    // Get the sidecar command
    let sidecar_command = app_handle
        .shell()
        .sidecar("team_bye_detector")
        .map_err(|e| format!("Failed to get sidecar command: {}", e))?;

    // Start the process
    let (_rx, child) = sidecar_command
        .args(&["--threshold", "0.4", "--cooldown", "2.0"])
        .spawn()
        .map_err(|e| format!("Failed to start wake word detector: {}", e))?;

    info!("Wake word detector started with PID: {:?}", child.pid());

    // Store the process - Note: Tauri shell Child doesn't support the same methods as std::process::Child
    // We'll just mark as running since we can't easily manage the Tauri shell process
    *is_running = true;

    // For now, we let the process run independently
    // The wake word detector will print to its own stdout/stderr
    // Events will be handled through file monitoring or other mechanisms if needed
    
    info!("Wake word detector is now running independently");

    Ok("Wake word detection started successfully".to_string())
}

#[tauri::command]
pub async fn stop_wake_word_detection(state: State<'_, WakeWordState>) -> Result<String, String> {
    let mut is_running = state.is_running.lock().unwrap();
    
    if !*is_running {
        return Ok("Wake word detection is not running".to_string());
    }

    // Note: With the current implementation, we can't easily stop the Tauri shell process
    // The process will continue running until the main application exits
    warn!("Cannot stop wake word detector - it will run until application exits");
    *is_running = false;
    
    Ok("Wake word detection marked as stopped (process still running)".to_string())
}

#[tauri::command]
pub async fn get_wake_word_status(state: State<'_, WakeWordState>) -> Result<bool, String> {
    let is_running = state.is_running.lock().unwrap();
    Ok(*is_running)
}

// Auto-start wake word detection (called at app startup)
pub async fn auto_start_wake_word_detection(app_handle: AppHandle) -> Result<(), String> {
    info!("🎙️ Auto-starting wake word detection...");
    
    // Get the sidecar command
    let sidecar_command = app_handle
        .shell()
        .sidecar("team_bye_detector")
        .map_err(|e| format!("Failed to get sidecar command: {}", e))?;

    // Start the process
    let (_rx, _child) = sidecar_command
        .args(&["--threshold", "0.3", "--cooldown", "1.5"])
        .spawn()
        .map_err(|e| format!("Failed to start wake word detector: {}", e))?;

    info!("🎙️ Wake word detector auto-started with PID: {:?}", _child.pid());

    // The process will run independently
    // Output will be visible in the terminal where Tauri was started
    info!("Wake word detector is running in the background");
    
    // Emit a debug log event to indicate successful start
    if let Err(e) = app_handle.emit("debug-log", serde_json::json!({
        "message": "Wake word detection started successfully",
        "tag": "WAKE_WORD_AUTO_START", 
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "level": "INFO"
    })) {
        error!("Failed to emit debug log event: {}", e);
    }
    
    Ok(())
}