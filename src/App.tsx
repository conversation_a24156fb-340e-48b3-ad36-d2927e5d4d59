import React, { useRef, useEffect } from "react";
import { TopBar } from "./components/topbar";
import { UserProvider, useUser } from "./context/UserContext";
import { LoginPage } from "./components/LoginPage";
import { AuthProvider, useAuth } from "./context/AuthContext";
import { ToastProvider } from "./context/ToastContext";
import { ToastContainer } from "./components/ui/Toast";
import { Skeleton } from "./components/ui/Skeleton";

import { TimeTrackingApp } from "./apps/time-tracking/TimeTrackingApp";
import { TimeTrackingReportsPage } from "./apps/time-tracking/TimeTrackingReportsPage";
import { ChatApp } from "./apps/chat/ChatApp";
import type { ChatConversation } from "./apps/chat/types";
import { ProjectApp } from "./apps/project/ProjectApp";
import { GanttChartPage } from "./apps/project/pages/GanttChartPage";
import { FileApp } from "./apps/file/FileApp";
import { TeamSidebar } from "./components/team-sidebar";
import { MiniSidebar } from "./components/mini-sidebar";
import { ExpandedSidebar } from "./components/expanded-sidebar";
import { TimeTrackingProvider } from "./context/TimeTrackingContext";
import { useCentrifugo } from "./hooks/useCentrifugo";
import { PulseBoardApp } from "./apps/pulse-board/PulseBoardApp";
import { OrganizationalRequestsApp } from "./apps/requests/OrganizationalRequestsApp";
import { MeetApp } from "./apps/meet/MeetApp";
import type { Call } from "./apps/meet/types";
import { useLocation } from "react-router-dom";
import { AIReportsPage } from "./apps/time-tracking/AIReportsPage";
import LiveKitBrowserView from "./modules/centrifugo/LiveKitBrowserView";
import { useGlobalShortcuts } from "./hooks/useGlobalShortcuts";
import { usePlatform } from "./utils/platform";
// LoginPage is already imported above

// Main App Component (after authentication)
function MainApp() {
  const [activeApp, setActiveApp] = React.useState("time");
  const [showEmployeeList, setShowEmployeeList] = React.useState(false);
  const { logout } = useAuth();
  const { isLinux } = usePlatform();

  // Initialize global shortcuts
  useGlobalShortcuts();

  // Initialize Centrifugo service
  const centrifugo = useCentrifugo();

  // Debug: Log Centrifugo status
  React.useEffect(() => {
    console.log('🔍 [APP] Centrifugo status:', centrifugo.status);
    console.log('🔍 [APP] Is connected:', centrifugo.isConnected);
    console.log('🔍 [APP] Is connecting:', centrifugo.isConnecting);
  }, [centrifugo.status, centrifugo.isConnected, centrifugo.isConnecting]);

  // Debug log
  React.useEffect(() => {
    console.log('🏠 [APP] showEmployeeList:', showEmployeeList);
  }, [showEmployeeList]);

  // React will NOT close splash screen - let Rust handle it after 9 seconds
  useEffect(() => {
    // React app is ready, but splash screen will be closed by Rust after 9 seconds
    console.log('React app is ready, waiting for Rust to close splash screen after 9 seconds');
  }, []);
  
  // Track which app icon is currently hovered in MiniSidebar
  const [hoveredApp, setHoveredApp] = React.useState<string | null>(null);
  // Keep ExpandedSidebar open while mouse is over it
  const [isExpandedHovering, setIsExpandedHovering] = React.useState(false);
  
  // Chat specific states
  const [chatConversation, setChatConversation] = React.useState<ChatConversation | null>(null);
  const [chatSearchQuery, setChatSearchQuery] = React.useState("");
  
  // Meet specific states
  const [meetCall, setMeetCall] = React.useState<Call | null>(null);
  
  type TimeSection = 'home' | 'reports' | 'ai-reports';
  type ProjectSection = 'projects' | 'boards' | 'gantt';
  const location = useLocation();
  
  // Derive timeSection from current pathname
  const timeSection: TimeSection = React.useMemo(() => {
    if (location.pathname.startsWith("/time-tracking") && location.pathname.includes("/ai-reports")) {
      return "ai-reports";
    }
    if (location.pathname.startsWith("/time-tracking") && location.pathname.includes("/reports")) {
      return "reports";
    }
    return "home";
  }, [location.pathname]);
  
  // Derive projectSection from current pathname
  const projectSection: ProjectSection = React.useMemo(() => {
    if (location.pathname.startsWith("/project") && location.pathname.includes("/gantt")) {
      return "gantt";
    }
    if (location.pathname.startsWith("/project") && location.pathname.includes("/boards")) {
      return "boards";
    }
    return "projects";
  }, [location.pathname]);
  
  // Close TeamSidebar when switching away from time tracking app
  React.useEffect(() => {
    if (activeApp !== "time") {
      setShowEmployeeList(false);
    }
    // Note: We don't auto-open for time app - user must manually toggle
  }, [activeApp]);
  

  // Render the active app content based on state
  const renderActiveApp = () => {
    switch (activeApp) {
      case "time":
        if (timeSection === 'reports') return <TimeTrackingReportsPage />;
        if (timeSection === 'ai-reports') return <AIReportsPage />;
        return <TimeTrackingApp />;
      case "chat":
        return (
          <ChatApp 
            conversation={chatConversation}
            onSelectConversation={setChatConversation}
          />
        );
      case "project":
        if (projectSection === 'gantt') return <GanttChartPage />;
        return <ProjectApp />;
      case "file":
        return <FileApp />;
      case "pulse":
        return <PulseBoardApp />;
      case "requests":
        return <OrganizationalRequestsApp />;
      case "meet":
        return (
          <MeetApp
            call={meetCall}
            onSelectCall={setMeetCall}
          />
        );
      default:
        return <TimeTrackingApp />;
    }
  };

  
  return (
    <TimeTrackingProvider>
      <div className="flex h-screen">
        {/* Sidebar wrapper: mini sidebar always visible */}
        <div className="relative">
          {/* Mini (collapsed) sidebar */}
          <MiniSidebar
            activeApp={activeApp}
            onChangeApp={setActiveApp}
            onHoverApp={setHoveredApp}
          />

          {/* Expanded sidebar - only show when activeApp is selected */}
          {(hoveredApp === activeApp || isExpandedHovering) && (
            <div
              className="fixed top-[60px] left-[68px] z-[9980] h-[calc(100vh-60px)]"
              onMouseEnter={() => setIsExpandedHovering(true)}
              onMouseLeave={() => setIsExpandedHovering(false)}
            >
              <ExpandedSidebar
                activeApp={activeApp}
                globalActiveApp={activeApp}
                chatConversation={chatConversation}
                onSelectChatConversation={setChatConversation}
                chatSearchQuery={chatSearchQuery}
                onChatSearchChange={setChatSearchQuery}
                meetCall={meetCall}
                onSelectMeetCall={setMeetCall}
              />
            </div>
          )}
        </div>

        <div className="flex flex-col flex-grow overflow-hidden">
          <TopBar
            showEmployeeList={showEmployeeList}
            toggleEmployeeList={() => setShowEmployeeList(prev => !prev)}
            activeApp={activeApp}
            onLogout={logout}
          />

          {/* Content area with persistent sidebar layout */}
          <div className="flex-grow overflow-hidden relative">
            <main
              className={`transition-all duration-300 ease-in-out h-full w-full ${
                ["chat", "meet"].includes(activeApp) 
                  ? "overflow-hidden chat-main-content" 
                  : isLinux 
                    ? "linux-hide-scrollbar" 
                    : "overflow-y-auto auto-hide-scrollbar"
              }`}
              style={{
                paddingRight: showEmployeeList ? '260px' : '0px',
                transition: 'padding-right 300ms ease-in-out'
              }}
              id="main-content"
            >


              {renderActiveApp()}
            </main>

            {/* Persistent Team sidebar - positioned absolutely but persistent */}
            <TeamSidebar
              isOpen={showEmployeeList}
              onClose={() => setShowEmployeeList(false)}
            />
          </div>
        </div>
      </div>
    </TimeTrackingProvider>
  );
}

// Auth Wrapper Component
function AuthWrapper() {
  const { isAuthenticated, isLoading, checkAuth } = useAuth();
  const { fetchUserProfile, clearUser, loadCachedProfile, loadActivityStatsFromStore } = useUser();
  const hasInitializedRef = useRef(false);
  const location = useLocation();

  // Handle special routes that don't require authentication
  if (location.pathname.startsWith("/login")) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-custom-background p-4">
        <LoginPage onLoginSuccess={(token) => {}} />
      </div>
    );
  }

  // Render LiveKit Browser View without authentication and context providers
  if (location.pathname.startsWith("/livekit-browser")) {
    return (
      <div className="min-h-screen bg-gray-900">
        <LiveKitBrowserView />
      </div>
    );
  }

  // Load cached profile on startup, then fetch fresh data when authenticated
  useEffect(() => {
    if (isAuthenticated && !hasInitializedRef.current) {
      console.log('🔐 [AUTH] User is authenticated, initializing profile loading...');
      hasInitializedRef.current = true;

      // Load cached profile and activity stats immediately for better UX
      loadCachedProfile().then(() => {
        // Load cached activity stats
        loadActivityStatsFromStore();

        // Then fetch fresh profile data
        console.log('🔐 [AUTH] Fetching fresh profile data from AuthWrapper...');
        fetchUserProfile();
      });
    } else if (!isAuthenticated) {
      console.log('🔐 [AUTH] User is not authenticated, clearing profile...');
      hasInitializedRef.current = false;
      clearUser();
    } else if (isAuthenticated && hasInitializedRef.current) {
      console.log('🔐 [AUTH] User already initialized, skipping...');
    }
  }, [isAuthenticated]); // Only depend on isAuthenticated to prevent infinite loops

  if (isLoading) {
    // Show skeleton loading while checking authentication
    return (
      <div className="min-h-screen bg-custom-background flex flex-col">
        {/* TopBar Skeleton */}
        <div className="h-10 bg-custom-sidebar border-b border-custom-border flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <Skeleton width={120} height={20} rounded="sm" />
          </div>
          <div className="flex items-center space-x-3">
            <Skeleton width={80} height={24} rounded="sm" />
            <Skeleton width={24} height={24} rounded="full" />
            <Skeleton width={24} height={24} rounded="full" />
            <Skeleton width={24} height={24} rounded="full" />
          </div>
        </div>

        {/* Main Content Skeleton */}
        <div className="flex-1 flex">
          {/* Sidebar Skeleton */}
          <div className="w-16 bg-custom-sidebar border-r border-custom-border flex flex-col items-center py-4 space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} width={32} height={32} rounded="lg" />
            ))}
          </div>

          {/* Content Area Skeleton */}
          <div className="flex-1 p-6 space-y-4">
            <Skeleton width="40%" height={32} rounded="sm" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="bg-custom-card border border-custom-border rounded-lg p-4 space-y-3">
                  <Skeleton width="100%" height={20} rounded="sm" />
                  <Skeleton width="80%" height={16} rounded="sm" />
                  <Skeleton width="60%" height={16} rounded="sm" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <LoginPage
        onLoginSuccess={(_token) => {
          // Refresh auth state after successful login
          checkAuth();
        }}
      />
    );
  }

  return <MainApp />;
}

// Main App Export with Auth Provider
export default function App() {
  console.log('🚀 [APP] App component rendering');

  // Check if this is the LiveKit browser route before loading providers
  const currentPath = window.location.hash.replace('#', '');
  if (currentPath.startsWith('/livekit-browser')) {
    console.log('🌐 [APP] Rendering LiveKit browser view without providers');
    return (
      <div className="min-h-screen bg-gray-900">
        <LiveKitBrowserView />
      </div>
    );
  }

  return (
    <AuthProvider>
      <ToastProvider>
        <UserProvider>
          <AuthWrapper />
          <ToastContainer />
        </UserProvider>
      </ToastProvider>
    </AuthProvider>
  );
}