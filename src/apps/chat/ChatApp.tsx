import React, { useState } from "react";
import { ChatView } from "./components/ChatView";
import { ChatSidebar } from "./components/ChatSidebar";
import { ChatTransition } from "./components/ChatTransition";
import { ChatErrorBoundary } from "./components/ChatErrorBoundary";
import { useChatState } from "./hooks/useChatState";
import type { ChatConversation } from "./types";
import { TauriIcon as Icon } from "../../components/TauriIcon";

interface ChatAppProps {
  conversation?: ChatConversation | null;
  onSelectConversation?: (conv: ChatConversation | null) => void;
}

export const ChatApp: React.FC<ChatAppProps> = ({ conversation = null, onSelectConversation }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(conversation);
  const [typingConversationId, setTypingConversationId] = useState<string | null>(null);
  const chatState = useChatState();
  
  const handleSelectConversation = (conv: ChatConversation | null) => {
    setSelectedConversation(conv);
    onSelectConversation?.(conv);
  };

  const handleTypingInConversation = (conversationId: string) => {
    setTypingConversationId(conversationId);
  };

  return (
    <ChatErrorBoundary>
      <div className="h-full flex">
        {/* Chat Sidebar */}
        <aside className="w-[320px] bg-custom-sidebar flex flex-col h-full border-r border-custom-border">
          <div className="flex-1 overflow-hidden">
            <ChatSidebar
              activeConversationId={selectedConversation?.id}
              onSelectConversation={handleSelectConversation}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              typingConversationId={typingConversationId}
            />
          </div>
        </aside>

        {/* Chat View */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <ChatTransition conversation={selectedConversation}>
            <ChatView
              conversation={selectedConversation}
              onBack={() => handleSelectConversation(null)}
              chatState={chatState}
              onTyping={handleTypingInConversation}
            />
          </ChatTransition>
        </div>
      </div>
    </ChatErrorBoundary>
  );
};