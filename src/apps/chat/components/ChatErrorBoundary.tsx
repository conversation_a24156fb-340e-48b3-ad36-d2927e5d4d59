import React, { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Card, CardBody, Button } from '@heroui/react';
import { TauriIcon as Icon } from '../../../components/TauriIcon';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ChatErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console
    console.error('Chat Error <PERSON>ry caught an error:', error, errorInfo);
    
    // Check if it's a ResizeObserver error
    if (error.message && error.message.includes('ResizeObserver')) {
      console.warn('Caught ResizeObserver error in boundary, attempting to recover...');
      
      // Try to recover by resetting the error state after a short delay
      setTimeout(() => {
        this.setState({
          hasError: false,
          error: null,
          errorInfo: null
        });
      }, 100);
    } else {
      // For other errors, update the state with error details
      this.setState({
        errorInfo
      });
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Check if it's a ResizeObserver error that we're trying to recover from
      if (this.state.error.message && this.state.error.message.includes('ResizeObserver')) {
        // Return children while we attempt to recover
        return this.props.children;
      }

      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="flex items-center justify-center h-full p-4">
          <Card className="max-w-md w-full bg-custom-card border-custom-border">
            <CardBody className="text-center py-8">
              <div className="w-16 h-16 bg-danger/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon="lucide:alert-triangle" className="text-danger text-2xl" />
              </div>
              <h3 className="text-white text-lg font-semibold mb-2">
                Something went wrong
              </h3>
              <p className="text-custom-muted text-sm mb-4">
                {this.state.error.message || 'An unexpected error occurred in the chat component'}
              </p>
              <Button
                color="primary"
                variant="flat"
                onPress={this.handleReset}
                startContent={<Icon icon="lucide:refresh-cw" />}
              >
                Try Again
              </Button>
              
              {/* Show details if available */}
              {this.state.errorInfo && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-custom-muted text-xs">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-custom-sidebar p-2 rounded overflow-auto max-h-40">
                    {this.state.error.stack}
                    {'\n\n'}
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </CardBody>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook to reset error boundary from child components
export const useChatErrorBoundary = () => {
  const [resetKey, setResetKey] = React.useState(0);
  
  const reset = () => {
    setResetKey(prev => prev + 1);
  };
  
  return { resetKey, reset };
};