import React, { useState, useMemo } from "react";
import { Card, CardBody, CardHeader, Input, Avatar, Badge, Button, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { TauriIcon as Icon } from "../../../components/TauriIcon";
import { mockConversations } from "../data/mockData";
import type { ChatConversation } from "../types";
import { CreateChannelGroupModal } from "./CreateChannelGroupModal";
import { useAutoHideScrollbar } from "../../../hooks/useAutoHideScrollbar";

interface ChatSidebarProps {
  activeConversationId?: string;
  onSelectConversation: (conversation: ChatConversation) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  inChatSearchQuery?: string;
  typingConversationId?: string | null;
}

export const ChatSidebar: React.FC<ChatSidebarProps> = ({
  activeConversationId,
  onSelectConversation,
  searchQuery,
  onSearchChange,
  inChatSearchQuery = "",
  typingConversationId
}) => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'pinned' | 'channels'>('all');
  const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);
  const [isCreateChannelModalOpen, setIsCreateChannelModalOpen] = useState(false);
  
  const scrollRef = useAutoHideScrollbar<HTMLDivElement>({
    hideDelay: 2000,
    showOnHover: true
  });

  const filteredConversations = useMemo(() => {
    let result = [...mockConversations];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        conv => conv.title.toLowerCase().includes(query) || 
                conv.lastMessage?.content.toLowerCase().includes(query)
      );
      
      // When in-chat search is active, highlight the active conversation
      if (inChatSearchQuery && activeConversationId) {
        result = result.map(conv => ({
          ...conv,
          isHighlighted: conv.id === activeConversationId
        }));
      }
    }
    
    // Apply type filter
    if (filter === 'unread') {
      result = result.filter(conv => conv.unreadCount > 0);
    } else if (filter === 'pinned') {
      result = result.filter(conv => conv.isPinned);
    } else if (filter === 'channels') {
      result = result.filter(conv => conv.type === 'channel');
    }
    
    // Sort by typing status, pinned first, then by last message date
    return result.sort((a, b) => {
      // If one is being typed in, it goes first
      if (a.id === typingConversationId && b.id !== typingConversationId) return -1;
      if (a.id !== typingConversationId && b.id === typingConversationId) return 1;
      
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      const dateA = a.lastMessage?.timestamp || new Date(0);
      const dateB = b.lastMessage?.timestamp || new Date(0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [searchQuery, filter, inChatSearchQuery, activeConversationId, typingConversationId]);

  const formatTime = (date: Date) => {
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Yesterday';
    }
    
    // If it's within the last 7 days, show the day name
    const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }
    
    // Otherwise show the date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  const getConversationIcon = (conversation: ChatConversation) => {
    if (conversation.type === 'channel') {
      return (
        <div className="absolute bottom-0 right-0 bg-primary rounded-full w-4 h-4 flex items-center justify-center">
          <Icon icon="lucide:megaphone" className="text-white text-[10px]" />
        </div>
      );
    } else if (conversation.isOnline) {
      return (
        <div className="absolute bottom-0 right-0 bg-green-500 rounded-full w-3 h-3 border-2 border-[#0E1120]" />
      );
    }
    
    return null;
  };

  const handleCreateSuccess = (newConversation: ChatConversation) => {
    // This would typically update the conversations list via a context or redux
    // For now we'll just log it
    console.log("Created new conversation:", newConversation);
    
    // In a real app, you would add this to your conversations list
    // For example:
    // addConversation(newConversation);
    
    // And then select it
    onSelectConversation(newConversation);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search and Add Button Row */}
      <div className="p-3 border-b border-custom-border">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search chats..."
            value={searchQuery}
            onValueChange={onSearchChange}
            startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
            endContent={
              searchQuery ? (
                <Button
                  isIconOnly
                  variant="light"
                  size="sm"
                  className="text-custom-muted hover:text-custom-text min-w-6 w-6 h-6"
                  onPress={() => onSearchChange("")}
                >
                  <Icon icon="lucide:x" className="text-sm" />
                </Button>
              ) : null
            }
            classNames={{
              base: "flex-1",
              inputWrapper: "bg-custom-sidebar border-custom-border h-10",
            }}
            radius="lg"
            variant="bordered"
          />
          
          <Dropdown>
            <DropdownTrigger>
              <Button
                isIconOnly
                variant="light"
                size="sm"
                className="text-custom-text min-w-10 w-10 h-10"
              >
                <Icon icon="lucide:plus" className="text-lg" />
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Create new" className="bg-[#1A1D2B] border border-[#2A2D3C]">
              <DropdownItem
                key="new-group"
                startContent={<Icon icon="lucide:users" className="text-primary" />}
                onPress={() => setIsCreateGroupModalOpen(true)}
                className="text-white"
              >
                Create Group
              </DropdownItem>
              <DropdownItem
                key="new-channel"
                startContent={<Icon icon="lucide:megaphone" className="text-primary" />}
                onPress={() => setIsCreateChannelModalOpen(true)}
                className="text-white"
              >
                Create Channel
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
      
      {/* Filter Buttons Row */}
      <div className="flex items-center px-3 py-2 border-b border-custom-border gap-2">
        <div className="flex flex-1 gap-1">
          <Button
            size="sm"
            variant={filter === 'all' ? "flat" : "light"}
            color={filter === 'all' ? "primary" : "default"}
            onPress={() => setFilter('all')}
          >
            All
          </Button>
          <Button
            size="sm"
            variant={filter === 'unread' ? "flat" : "light"}
            color={filter === 'unread' ? "primary" : "default"}
            onPress={() => setFilter('unread')}
          >
            Unread
          </Button>
          <Button
            size="sm"
            variant={filter === 'pinned' ? "flat" : "light"}
            color={filter === 'pinned' ? "primary" : "default"}
            onPress={() => setFilter('pinned')}
          >
            Pinned
          </Button>
          <Button
            size="sm"
            variant={filter === 'channels' ? "flat" : "light"}
            color={filter === 'channels' ? "primary" : "default"}
            onPress={() => setFilter('channels')}
          >
            Channels
          </Button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto chat-scrollbar" ref={scrollRef}>
        {filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-custom-muted">
            <Icon icon="lucide:search-x" className="text-3xl mb-2" />
            <p>No conversations found</p>
          </div>
        ) : (
          filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`flex items-center p-3 border-b border-custom-border cursor-pointer hover:bg-custom-border/10 relative transition-colors
                ${activeConversationId === conversation.id ? 'bg-primary/20 border-l-4 border-l-primary' : ''}
                ${conversation.isPinned && activeConversationId !== conversation.id ? 'bg-custom-border/5' : ''}
                ${conversation.isHighlighted ? 'bg-primary/10' : ''}
              `}
              onClick={() => onSelectConversation(conversation)}
            >
              <div className="relative">
                <Avatar
                  src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
                  className="h-10 w-10"
                />
                {getConversationIcon(conversation)}
              </div>
              
              <div className="ml-3 flex-1 min-w-0">
                <div className="flex items-center">
                  <p className="text-white text-sm font-medium truncate max-w-[120px]">
                    {conversation.title}
                  </p>
                  {conversation.type !== 'private' && (
                    <span className="ml-1.5 text-[10px] px-1.5 py-0.5 rounded-full bg-[#2A2D3C] text-[#C0C4CC]">
                      {conversation.type === 'channel' ? 'channel' : 'group'}
                    </span>
                  )}
                  <span className="ml-auto text-custom-muted text-xs whitespace-nowrap">
                    {conversation.lastMessage && formatTime(conversation.lastMessage.timestamp)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-custom-muted text-xs truncate max-w-[150px]">
                    {conversation.isTyping ? (
                      <span className={conversation.type === 'private' ? "text-custom-primary" : "text-primary"}>typing...</span>
                    ) : conversation.lastMessage ? (
                      <>
                        {((conversation.type !== 'private' && conversation.lastMessage.senderId !== 'me') || conversation.type === 'channel') && (
                          <span className="font-medium mr-1">{conversation.lastMessage.senderName || 'User'}:</span>
                        )}
                        {conversation.lastMessage.content}
                      </>
                    ) : (
                      'No messages yet'
                    )}
                  </p>
                  <div className="flex items-center">
                    {conversation.isMuted && (
                      <Icon icon="lucide:volume-x" className="text-custom-muted text-xs mr-1" />
                    )}
                    {conversation.unreadCount > 0 && (
                      <span className="bg-primary text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                        {conversation.unreadCount}
                      </span>
                    )}
                    {conversation.lastMessage?.senderId === 'me' && (
                      <div className="text-xs text-custom-muted ml-1">
                        {conversation.lastMessage.status === 'read' ? (
                          <Icon icon="lucide:check-check" className="text-primary text-xs" />
                        ) : (
                          <Icon icon="lucide:check" className="text-custom-muted text-xs" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <CreateChannelGroupModal
        isOpen={isCreateGroupModalOpen}
        onClose={() => setIsCreateGroupModalOpen(false)}
        onCreateSuccess={handleCreateSuccess}
        type="group"
      />
      
      <CreateChannelGroupModal
        isOpen={isCreateChannelModalOpen}
        onClose={() => setIsCreateChannelModalOpen(false)}
        onCreateSuccess={handleCreateSuccess}
        type="channel"
      />
    </div>
  );
};