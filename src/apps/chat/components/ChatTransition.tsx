import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import type { ChatConversation } from '../types';

interface ChatTransitionProps {
  conversation: ChatConversation | null;
  children: React.ReactNode;
}

export const ChatTransition: React.FC<ChatTransitionProps> = ({ conversation, children }) => {
  // Check for reduced motion preference
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  if (prefersReducedMotion) {
    return <>{children}</>;
  }

  // Simple fade transition like Telegram Web
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={conversation?.id || 'empty'}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.15, ease: 'easeInOut' }}
        className="h-full"
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

// Header transition component - simplified
export const ChatHeaderTransition: React.FC<{
  conversation: ChatConversation | null;
  children: React.ReactNode;
}> = ({ conversation, children }) => {
  return <>{children}</>;
};

// Message list transition - simplified
export const MessageListTransition: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return <div className="h-full">{children}</div>;
};

// Individual message animation - only for new messages
export const MessageTransition: React.FC<{
  children: React.ReactNode;
  index: number;
  isNew?: boolean;
}> = ({ children, isNew = false }) => {
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  if (prefersReducedMotion || !isNew) {
    return <>{children}</>;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.2,
        ease: 'easeOut',
      }}
    >
      {children}
    </motion.div>
  );
};