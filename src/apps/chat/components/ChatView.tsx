import React, { useState, useRef, useEffect } from "react";
import { Card, CardBody, CardHeader, Input, Avatar, Button, Tooltip, Dropdown, DropdownTrigger, DropdownMenu, Dropdown<PERSON>tem, Badge, Modal, ModalContent, <PERSON>dalHeader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { TauriIcon as Icon } from "../../../components/TauriIcon";
import { type ChatConversation, type ChatMessage } from "../types";
import { mockUsers, mockMessages } from "../data/mockData";
import { MessageBubble } from "./MessageBubble";
import { NewMessageAlert } from "./NewMessageAlert";
import { MessageInput, type MessageInputRef } from "./MessageInput";
import { CreateChannelGroupModal } from "./CreateChannelGroupModal";
import { ChatHeaderTransition, MessageListTransition, MessageTransition } from "./ChatTransition";
import { useRestoreScroll } from "../hooks/useChatState";
import { useAutoHideScrollbar } from "../../../hooks/useAutoHideScrollbar";

interface ChatViewProps {
  conversation: ChatConversation | null;
  onBack: () => void;
  chatState?: {
    saveScrollPosition: (chatId: string, position: number) => void;
    getScrollPosition: (chatId: string) => number;
    saveDraftMessage: (chatId: string, message: string) => void;
    getDraftMessage: (chatId: string) => string;
    clearChatState: (chatId: string) => void;
  };
  onTyping?: (conversationId: string) => void;
}

export const ChatView: React.FC<ChatViewProps> = ({ conversation, onBack, chatState, onTyping }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [showNewMessageAlert, setShowNewMessageAlert] = useState(false);
  const [replyTo, setReplyTo] = useState<ChatMessage | null>(null);
  const [isPinnedMessagesOpen, setIsPinnedMessagesOpen] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<ChatMessage[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [pinnedMessages, setPinnedMessages] = useState<ChatMessage[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadedAllMessages, setLoadedAllMessages] = useState(false);
  const MESSAGES_PER_PAGE = 20;
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useAutoHideScrollbar<HTMLDivElement>({
    hideDelay: 2000,
    showOnHover: true
  });
  const messageInputRef = useRef<MessageInputRef>(null);
  const [userScrolled, setUserScrolled] = useState(false);
  const scrollPositionRef = useRef<number>(0);
  const scrollHeightRef = useRef<number>(0);
  
  // Use restore scroll hook
  useRestoreScroll(
    conversation?.id,
    messagesContainerRef,
    chatState?.getScrollPosition || (() => 0)
  );
  
  // Load messages and pinned messages when conversation changes
  useEffect(() => {
    if (conversation) {
      // Reset pagination and load only the most recent messages
      setPage(1);
      setHasMore(true);
      setLoadedAllMessages(false);
      
      const allMessages = mockMessages[conversation.id] || [];
      const startIdx = Math.max(0, allMessages.length - MESSAGES_PER_PAGE);
      const initialMessages = allMessages.slice(startIdx);
      setMessages(initialMessages);
      
      // Filter out pinned messages
      const pinned = allMessages.filter(message => message.isPinned);
      setPinnedMessages(pinned);
      
      setUserScrolled(false);
      setShowNewMessageAlert(false); // Hide alert when switching conversations
      setTimeout(scrollToBottom, 100);
    }
  }, [conversation]);
  
  // Add search functionality
  useEffect(() => {
    if (searchQuery.trim() && messages.length) {
      const query = searchQuery.toLowerCase();
      const results = messages.filter(message => 
        message.content.toLowerCase().includes(query)
      );
      setSearchResults(results);
      setCurrentSearchIndex(results.length > 0 ? 0 : -1);
      
      // Scroll to first result if found
      if (results.length > 0) {
        const messageEl = document.getElementById(`message-${results[0].id}`);
        messageEl?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    } else {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
    }
  }, [searchQuery, messages]);

  // Handle navigation between search results
  const navigateSearch = (direction: 'next' | 'prev') => {
    if (searchResults.length === 0) return;
    
    let newIndex = currentSearchIndex;
    if (direction === 'next') {
      newIndex = (currentSearchIndex + 1) % searchResults.length;
    } else {
      newIndex = (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    }
    
    setCurrentSearchIndex(newIndex);
    
    // Scroll to the message
    const messageEl = document.getElementById(`message-${searchResults[newIndex].id}`);
    messageEl?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };
  
  const closeSearch = () => {
    setIsSearchActive(false);
    setSearchQuery("");
    setSearchResults([]);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  // Detect when user is scrolling near the top to load more messages
  const handleScroll = () => {
    if (!messagesContainerRef.current || !conversation) return;
    
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    // Check if scrolled to bottom (for new message alert)
    // Increase threshold to 50px to account for rounding errors
    const isScrolledToBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 50;
    setUserScrolled(!isScrolledToBottom);
    
    // Save scroll position
    if (chatState) {
      chatState.saveScrollPosition(conversation.id, scrollTop);
    }
    
    // Check if scrolled near the top (for loading older messages)
    if (scrollTop < 100 && hasMore && !isLoadingMore && !isPinnedMessagesOpen) {
      loadOlderMessages();
    }
  };
  
  const loadOlderMessages = () => {
    if (!conversation || isLoadingMore || !hasMore) return;
    
    setIsLoadingMore(true);
    
    // Save current scroll position and height
    if (messagesContainerRef.current) {
      scrollPositionRef.current = messagesContainerRef.current.scrollTop;
      scrollHeightRef.current = messagesContainerRef.current.scrollHeight;
    }
    
    // Simulate API call delay
    setTimeout(() => {
      const allMessages = mockMessages[conversation.id] || [];
      const nextPage = page + 1;
      const startIdx = Math.max(0, allMessages.length - (nextPage * MESSAGES_PER_PAGE));
      const endIdx = allMessages.length - ((page) * MESSAGES_PER_PAGE);
      
      // No more messages to load
      if (startIdx === 0) {
        setHasMore(false);
        setLoadedAllMessages(true);
      }
      
      // Get older messages
      const olderMessages = allMessages.slice(startIdx, endIdx);
      
      // Add older messages to the beginning of current messages
      setMessages(prev => [...olderMessages, ...prev]);
      setPage(nextPage);
      setIsLoadingMore(false);
      
      // After state update and render, restore scroll position
      setTimeout(() => {
        if (messagesContainerRef.current) {
          const newScrollHeight = messagesContainerRef.current.scrollHeight;
          const scrollDifference = newScrollHeight - scrollHeightRef.current;
          messagesContainerRef.current.scrollTop = scrollPositionRef.current + scrollDifference;
        }
      }, 50);
    }, 1000); // Simulate network delay
  };
  
  const handleSendMessage = (content: string, attachments?: any[]) => {
    if (!conversation || (!content.trim() && !attachments?.length)) return;
    
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: 'me',
      senderName: 'Me',
      content,
      timestamp: new Date(),
      status: 'sent',
      isOutgoing: true,
      replyTo: replyTo ? {
        id: replyTo.id,
        content: replyTo.content,
        senderName: replyTo.senderName
      } : undefined,
      attachments
    };
    
    setMessages(prev => [...prev, newMessage]);
    setReplyTo(null);
    
    // Always scroll to bottom when user sends a message
    setTimeout(scrollToBottom, 100);
    setUserScrolled(false);
    setShowNewMessageAlert(false); // Hide alert when user sends message
    
    // Simulate message being read after a delay
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id ? { ...msg, status: 'read' } : msg
        )
      );
    }, 2000);
    
    // Simulate reply after a delay
    if (Math.random() > 0.5) {
      setTimeout(() => {
        // Set typing indicator
        if (conversation) {
          conversation.isTyping = true;
          // Force re-render
          // This would normally be handled by a state management system
        }
        
        // Send response after "typing"
        setTimeout(() => {
          const responseMessage: ChatMessage = {
            id: `msg-${Date.now()}`,
            senderId: conversation.id,
            senderName: conversation.title,
            senderAvatar: conversation.avatar,
            content: getRandomResponse(),
            timestamp: new Date(),
            status: 'read',
            isOutgoing: false
          };
          
          setMessages(prev => [...prev, responseMessage]);
          
          if (conversation) {
            conversation.isTyping = false;
          }
          
          // Check again if user is scrolled before showing alert
          if (messagesContainerRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
            const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 50;
            
            if (!isAtBottom) {
              setShowNewMessageAlert(true);
            } else {
              setTimeout(scrollToBottom, 100);
            }
          }
        }, 1500);
      }, 1000);
    }
  };
  
  const getRandomResponse = () => {
    const responses = [
      "Got it, thanks!",
      "I'll take a look at this soon.",
      "Sounds good to me.",
      "Can we discuss this in our next meeting?",
      "Thanks for the update!",
      "I'll get back to you on this.",
      "Let me check with the team.",
      "Great progress!",
      "I have some feedback on this.",
      "Let's schedule a call to discuss."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };
  
  const handleNewMessageAlertClick = () => {
    scrollToBottom();
    setShowNewMessageAlert(false);
    setUserScrolled(false);
  };

  if (!conversation) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-custom-card">
        <div className="text-center p-6">
          <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon="lucide:message-circle" className="text-custom-muted text-3xl" />
          </div>
          <h3 className="text-white text-lg font-medium mb-2">Select a conversation</h3>
          <p className="text-custom-muted text-sm">Choose from your contacts to start chatting</p>
        </div>
      </div>
    );
  }

  const openParticipantsModal = () => {
    setShowParticipantsModal(true);
  };

  const handleEditSuccess = (updatedData: ChatConversation) => {
    console.log("Updated conversation:", updatedData);
    // In a real app, you would update the conversation in your state/context/store
    // For now just close the modal
    setIsEditModalOpen(false);
  };

  const isCurrentUserAdmin = () => {
    // In a real app, you would check the current user's ID against the admins array
    // For this example, we'll assume the current user is always admin
    return true;
  };

  const handleTogglePin = (messageId: string) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, isPinned: !msg.isPinned } 
          : msg
      )
    );
    
    // Update pinned messages list
    setPinnedMessages(prev => {
      const message = messages.find(m => m.id === messageId);
      if (!message) return prev;
      
      if (message.isPinned) {
        // Message is already pinned, so remove it
        return prev.filter(m => m.id !== messageId);
      } else {
        // Message is not pinned, so add it
        return [...prev, { ...message, isPinned: true }];
      }
    });
  };

  // توابع کمکی را به بالای فایل منتقل کن:
  function formatLastSeen(date: Date) {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    return date.toLocaleDateString();
  }
  function formatDateHeader(date: Date) {
    const now = new Date();
    const isToday = isSameDay(date, now);
    if (isToday) return 'Today';
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = isSameDay(date, yesterday);
    if (isYesterday) return 'Yesterday';
    const daysDiff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) return date.toLocaleDateString(undefined, { weekday: 'long' });
    return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
  }
  function isSameDay(date1: Date, date2: Date) {
    return date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate();
  }
  function shouldShowSender(message: any, index: number, messages: any[]) {
    if (index === 0) return true;
    const prevMessage = messages[index - 1];
    if (prevMessage.senderId !== message.senderId) return true;
    const timeDiff = message.timestamp.getTime() - prevMessage.timestamp.getTime();
    if (timeDiff > 2 * 60 * 1000) return true;
    return false;
  }
  
  // Handle click on messages container to focus input
  const handleMessagesContainerClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const target = e.target as HTMLElement;
    
    // More specific check for interactive elements
    const isInteractiveElement = (element: HTMLElement): boolean => {
      const tagName = element.tagName.toLowerCase();
      
      // Direct interactive elements
      if (tagName === 'button' || 
          tagName === 'a' || 
          tagName === 'input' || 
          tagName === 'textarea' ||
          tagName === 'select') {
        return true;
      }
      
      // Elements with specific interactive roles or classes
      if (element.getAttribute('role') === 'button' ||
          element.classList.contains('cursor-pointer') ||
          element.hasAttribute('data-slot') || // NextUI elements
          element.classList.contains('nextui-dropdown-trigger') ||
          element.closest('[data-slot="trigger"]')) {
        return true;
      }
      
      // Check if element is inside a button (for nested content)
      const parentButton = element.closest('button');
      if (parentButton) return true;
      
      // Check if element is inside a dropdown or popover
      const parentDropdown = element.closest('[role="menu"], [role="listbox"], .nextui-dropdown, [data-slot="menu"]');
      if (parentDropdown) return true;
      
      return false;
    };
    
    // Only focus input if the clicked element is not interactive
    if (!isInteractiveElement(target)) {
      // Use a small timeout to ensure the focus works properly
      setTimeout(() => {
        messageInputRef.current?.focus();
      }, 50);
    }
  };

  return (
    <>
      <div className="flex flex-col h-full bg-custom-card">
        <ChatHeaderTransition conversation={conversation}>
          <div className="border-b border-custom-border flex items-center justify-between p-3">
          {isSearchActive ? (
          <div className="flex items-center w-full">
            <Button 
              isIconOnly 
              variant="light" 
              size="sm" 
              className="text-custom-text mr-2"
              onPress={closeSearch}
            >
              <Icon icon="lucide:arrow-left" />
            </Button>
            <div className="flex-1 relative">
              <Input
                placeholder="Search in chat..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                autoFocus
                classNames={{
                  inputWrapper: "bg-custom-sidebar border-custom-border h-9",
                }}
                radius="lg"
                variant="bordered"
                size="sm"
                startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
                endContent={
                  searchQuery ? (
                    <Button 
                      isIconOnly 
                      variant="light" 
                      size="sm" 
                      className="text-custom-muted"
                      onPress={() => setSearchQuery("")}
                    >
                      <Icon icon="lucide:x" className="text-xs" />
                    </Button>
                  ) : null
                }
              />
            </div>
            <div className="flex items-center ml-2">
              {searchResults.length > 0 && (
                <span className="text-xs text-custom-text mr-2">
                  {currentSearchIndex + 1}/{searchResults.length}
                </span>
              )}
              <Button 
                isIconOnly 
                variant="light" 
                size="sm" 
                isDisabled={searchResults.length === 0}
                className="text-custom-text"
                onPress={() => navigateSearch('prev')}
              >
                <Icon icon="lucide:chevron-up" />
              </Button>
              <Button 
                isIconOnly 
                variant="light" 
                size="sm" 
                isDisabled={searchResults.length === 0}
                className="text-custom-text"
                onPress={() => navigateSearch('next')}
              >
                <Icon icon="lucide:chevron-down" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center">
            <Button 
              isIconOnly 
              variant="light" 
              size="sm" 
              className="text-custom-text md:hidden mr-2"
              onPress={onBack}
            >
              <Icon icon="lucide:chevron-left" />
            </Button>
            
            <Avatar
              src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
              className="h-10 w-10"
            />
            
            <div className="ml-3">
              <div className="flex items-center">
                <p className="text-white text-sm font-medium">{conversation.title}</p>
                {conversation.type === 'channel' && (
                  <Badge content="" color="primary" className="ml-2">
                    <Icon icon="lucide:megaphone" className="text-xs" />
                  </Badge>
                )}
              </div>
              <p className="text-custom-muted text-xs">
                {conversation.isTyping ? (
                  <span className="text-custom-primary">typing...</span>
                ) : conversation.isOnline ? (
                  'online'
                ) : conversation.lastSeen ? (
                  `last seen ${formatLastSeen(conversation.lastSeen)}`
                ) : conversation.type === 'group' ? (
                  `${conversation.members?.length || 0} members`
                ) : conversation.type === 'channel' ? (
                  conversation.description || 'Channel'
                ) : (
                  'offline'
                )}
              </p>
            </div>
          </div>
        )}
        
        {!isSearchActive && (
          <div className="flex items-center gap-1">
            {conversation.type === 'private' && (
              <>
                <Tooltip content="Voice call">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text"
                  >
                    <Icon icon="lucide:phone" />
                  </Button>
                </Tooltip>
                <Tooltip content="Video call">
                  <Button 
                    isIconOnly 
                    variant="light" 
                    className="text-custom-text"
                  >
                    <Icon icon="lucide:video" />
                  </Button>
                </Tooltip>
              </>
            )}
            
            {(conversation.type === 'group' || conversation.type === 'channel') && (
              <Tooltip content={`${conversation.type === 'channel' ? 'Channel' : 'Group'} info`}>
                <Button 
                  isIconOnly 
                  variant="light" 
                  className="text-custom-text"
                  onPress={openParticipantsModal}
                >
                  <Icon icon={conversation.type === 'channel' ? "lucide:info" : "lucide:users"} />
                </Button>
              </Tooltip>
            )}
            
            <Tooltip content="Pinned messages">
              <Button 
                isIconOnly 
                variant="light" 
                className={`text-custom-text ${isPinnedMessagesOpen ? 'bg-primary/10 text-primary' : ''}`}
                onPress={() => setIsPinnedMessagesOpen(!isPinnedMessagesOpen)}
              >
                <Icon icon="lucide:pin" />
                {pinnedMessages.length > 0 && (
                  <span className="absolute top-0 right-0 bg-primary text-white text-xs rounded-full min-w-[16px] h-[16px] flex items-center justify-center">
                    {pinnedMessages.length}
                  </span>
                )}
              </Button>
            </Tooltip>
            
            <Dropdown>
              <DropdownTrigger>
                <Button isIconOnly variant="light" className="text-custom-text">
                  <Icon icon="lucide:more-vertical" />
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Chat options" className="bg-[#1A1D2B] border border-[#2A2D3C]">
                <DropdownItem 
                  key="search-chat" 
                  startContent={<Icon icon="lucide:search" />}
                  onPress={() => setIsSearchActive(true)}
                  className="text-white"
                >
                  Search in chat
                </DropdownItem>
                <DropdownItem key="mute-chat" startContent={<Icon icon="lucide:bell" />} className="text-white">
                  Mute notifications
                </DropdownItem>
                
                {(conversation.type === 'group' || conversation.type === 'channel') && (
                  <>
                    <DropdownItem 
                      key="view-members" 
                      startContent={<Icon icon="lucide:users" />}
                      onPress={openParticipantsModal}
                      className="text-white"
                    >
                      View members
                    </DropdownItem>
                    
                    <DropdownItem 
                      key="manage-access" 
                      startContent={<Icon icon="lucide:shield" />}
                      className="text-white"
                    >
                      Manage access
                    </DropdownItem>
                    
                    {conversation.type === 'channel' && (
                      <DropdownItem 
                        key="channel-settings" 
                        startContent={<Icon icon="lucide:settings" />}
                        className="text-white"
                      >
                        Channel settings
                      </DropdownItem>
                    )}
                  </>
                )}
                
                <DropdownItem key="shared-media" startContent={<Icon icon="lucide:image" />} className="text-white">
                  Shared media
                </DropdownItem>
                <DropdownItem 
                  key="clear-history"
                  startContent={<Icon icon="lucide:trash-2" />}
                  className="text-danger"
                >
                  Clear history
                </DropdownItem>
                
                {(conversation.type === 'group' || conversation.type === 'channel') && (
                  <DropdownItem 
                    key="leave"
                    startContent={<Icon icon="lucide:log-out" />}
                    className="text-danger"
                  >
                    {conversation.type === 'channel' ? 'Leave channel' : 'Leave group'}
                  </DropdownItem>
                )}
              </DropdownMenu>
            </Dropdown>
            </div>
          )}
        </div>
        </ChatHeaderTransition>
      
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 overflow-hidden relative">
          <MessageListTransition>
            <div
              className="absolute inset-0 overflow-y-auto p-4 chat-scrollbar cursor-text"
              ref={messagesContainerRef}
              onScroll={handleScroll}
              onClick={handleMessagesContainerClick}
            >
          {isLoadingMore && (
            <div className="flex justify-center py-3">
              <Spinner size="sm" color="primary" />
            </div>
          )}
          
          {loadedAllMessages && !isPinnedMessagesOpen && messages.length > 0 && (
            <div className="text-center mb-4">
              <p className="text-custom-muted text-xs py-2 px-4 inline-block bg-[#12141F] rounded-full">
                Beginning of conversation
              </p>
            </div>
          )}
          
          {isPinnedMessagesOpen ? (
            pinnedMessages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-custom-muted">
                <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mb-4">
                  <Icon icon="lucide:pin" className="text-3xl" />
                </div>
                <p className="mb-2">No pinned messages</p>
                <p className="text-xs">Long press a message to pin it to this tab</p>
              </div>
            ) : (
              <>
                <div className="bg-[#12141F] p-2 border-b border-custom-border flex items-center justify-between">
                  <div className="flex items-center">
                    <Icon icon="lucide:pin" className="text-primary mr-2" />
                    <h3 className="text-white text-sm font-medium">Pinned Messages</h3>
                    <span className="ml-2 bg-[#2A2D3C] text-[#C0C4CC] text-xs rounded-full px-2 py-0.5">
                      {pinnedMessages.length}
                    </span>
                  </div>
                  <Button 
                    isIconOnly 
                    variant="light" 
                    size="sm" 
                    className="text-custom-muted"
                    onPress={() => setIsPinnedMessagesOpen(false)}
                  >
                    <Icon icon="lucide:x" className="text-xs" />
                  </Button>
                </div>
                <p className="text-custom-text text-center text-xs py-2 mb-4">
                  Displaying {pinnedMessages.length} pinned message{pinnedMessages.length !== 1 && 's'}
                </p>
                {pinnedMessages.map((message, index) => {
                  const isSearchResult = searchResults.some(result => result.id === message.id);
                  const isActiveSearchResult = searchResults[currentSearchIndex]?.id === message.id;
                  return (
                    <div
                      key={message.id}
                      id={`message-${message.id}`}
                      className={`${isSearchResult ? 'relative' : ''} mb-4`}
                    >
                      {isSearchResult && (
                        <div className={`absolute inset-0 -mx-2 rounded-md border-2 ${
                          isActiveSearchResult ? 'border-primary bg-primary/10' : 'border-default-500 bg-default-500/10'
                        } pointer-events-none`}></div>
                      )}
                      <MessageBubble 
                        message={message} 
                        showSender={true}
                        onReply={() => setReplyTo(message)}
                        isPinned={true}
                        onTogglePin={() => handleTogglePin(message.id)}
                      />
                      <div className="flex justify-center mt-2">
                        <Button 
                          size="sm" 
                          variant="flat" 
                          color="danger"
                          startContent={<Icon icon="lucide:pin-off" />}
                          onPress={() => handleTogglePin(message.id)}
                        >
                          Unpin Message
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </>
            )
          ) : (
            messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-custom-muted">
                <div className="w-16 h-16 bg-custom-sidebar rounded-full flex items-center justify-center mb-4">
                  <Icon icon="lucide:message-circle" className="text-3xl" />
                </div>
                <p className="mb-2">No messages yet</p>
                <p className="text-xs">Send a message to start the conversation</p>
              </div>
            ) : (
              <>
                {messages.length > 0 && (
                  <p className="text-custom-text text-center text-xs py-2 mb-4">
                    {formatDateHeader(messages[0].timestamp)} 
                  </p>
                )}
                
                {messages.map((message, index) => {
                  // Check if we need a date header between messages
                  const showDateHeader = index > 0 && 
                    !isSameDay(messages[index-1].timestamp, message.timestamp);
                  
                  // Check if this message is a search result
                  const isSearchResult = searchResults.some(result => result.id === message.id);
                  const isActiveSearchResult = searchResults[currentSearchIndex]?.id === message.id;
                  
                  return (
                    <React.Fragment key={message.id}>
                      {showDateHeader && (
                        <p className="text-custom-text text-center text-xs py-2 my-4">
                          {formatDateHeader(message.timestamp)}
                        </p>
                      )}
                      
                      <MessageTransition index={index}>
                        <div
                          id={`message-${message.id}`}
                          className={`${isSearchResult ? 'relative' : ''}`}
                        >
                          {isSearchResult && (
                            <div className={`absolute inset-0 -mx-2 rounded-md border-2 ${
                              isActiveSearchResult ? 'border-primary bg-primary/10' : 'border-default-500 bg-default-500/10'
                            } pointer-events-none`}></div>
                          )}
                          <MessageBubble
                            message={message}
                            showSender={shouldShowSender(message, index, messages)}
                            onReply={() => setReplyTo(message)}
                            isPinned={message.isPinned}
                            onTogglePin={() => handleTogglePin(message.id)}
                          />
                        </div>
                      </MessageTransition>
                    </React.Fragment>
                  );
                })}
                <div ref={messagesEndRef} />
              </>
            )
          )}
            </div>
          </MessageListTransition>
          
          {showNewMessageAlert && !isPinnedMessagesOpen && (
            <NewMessageAlert onClick={handleNewMessageAlertClick} />
          )}
        </div>
        
        <MessageInput
          ref={messageInputRef}
          onSendMessage={handleSendMessage}
          replyTo={replyTo}
          onCancelReply={() => setReplyTo(null)}
          conversationType={conversation.type}
          chatId={conversation.id}
          chatState={chatState}
          onTyping={() => onTyping?.(conversation.id)}
        />
      </div>
    </div>

    <Modal 
      isOpen={showParticipantsModal} 
      onClose={() => setShowParticipantsModal(false)}
      placement="center"
      size="2xl"
      classNames={{
        base: "bg-[#1A1D2B] border border-[#2A2D3C]",
        header: "border-b border-[#2A2D3C]",
        footer: "border-t border-[#2A2D3C]",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="text-white flex items-center justify-between w-full">
              <div className="flex items-center">
                <Icon 
                  icon={conversation.type === 'channel' ? "lucide:megaphone" : "lucide:users"} 
                  className="text-primary mr-2" 
                />
                {conversation.type === 'channel' ? 'Channel Info' : 'Group Info'}
              </div>
              {/* Add Edit button for admins */}
              {isCurrentUserAdmin() && (
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  startContent={<Icon icon="lucide:edit-3" />}
                  onPress={() => {
                    setIsEditModalOpen(true);
                    onClose(); // Close info modal when opening edit modal
                  }}
                >
                  Edit
                </Button>
              )}
            </ModalHeader>
            
            <ModalBody className="py-4">
              <div className="space-y-6">
                <div className="flex items-center">
                  <Avatar
                    src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
                    className="h-16 w-16"
                  />
                  <div className="ml-4">
                    <h3 className="text-white text-lg font-medium">{conversation.title}</h3>
                    {conversation.description && (
                      <p className="text-[#C0C4CC] text-sm mt-1">{conversation.description}</p>
                    )}
                    <p className="text-[#7D8597] text-xs mt-2">
                      {`${conversation.members?.length || 0} members`}
                    </p>
                  </div>
                </div>
                <Divider />
                <div className="flex items-center">
                  <Icon icon="lucide:users" className="text-primary mr-2" />
                  <h3 className="text-white text-lg font-medium">Members</h3>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  {conversation.members?.map(member => (
                    <div key={member.id} className="flex items-center">
                      <Avatar
                        src={member.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${member.id}`}
                        className="h-8 w-8"
                      />
                      <span className="text-white text-sm ml-2">{member.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="border-t border-[#2A2D3C]">
              <Button 
                color="danger" 
                variant="flat" 
                onPress={() => setShowParticipantsModal(false)}
              >
                Close
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>

    <Modal 
      isOpen={isEditModalOpen} 
      onClose={() => setIsEditModalOpen(false)}
      placement="center"
      size="2xl"
      classNames={{
        base: "bg-[#1A1D2B] border border-[#2A2D3C]",
        header: "border-b border-[#2A2D3C]",
        footer: "border-t border-[#2A2D3C]",
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="text-white flex items-center justify-between w-full">
              <div className="flex items-center">
                <Icon icon="lucide:edit-3" className="text-primary mr-2" />
                {conversation.type === 'channel' ? 'Channel Info' : 'Group Info'}
              </div>
              <Button 
                isIconOnly 
                variant="light" 
                onPress={onClose}
              >
                <Icon icon="lucide:x" />
              </Button>
            </ModalHeader>
            <ModalBody className="py-4">
              <div className="space-y-6">
                <div className="flex items-center">
                  <Avatar
                    src={conversation.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${conversation.id}`}
                    className="h-16 w-16"
                  />
                  <div className="ml-4">
                    <h3 className="text-white text-lg font-medium">{conversation.title}</h3>
                    <p className="text-[#C0C4CC] text-sm mt-1">{conversation.description}</p>
                  </div>
                </div>
              </div>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  </>
);
};
