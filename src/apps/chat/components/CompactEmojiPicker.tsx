import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, ScrollShadow } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';

interface CompactEmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  children: React.ReactElement<any>;
}

// Emoji categories like Telegram
const emojiCategories = {
  recent: ['😀', '😂', '❤️', '👍', '😊', '🎉', '😍', '🤔', '😭', '🙏'],
  smileys: [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋',
    '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐',
    '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '😔', '😪', '😴',
    '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '😵', '🤯',
    '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️', '😮', '😯', '😲',
    '😳', '🥺', '😦', '😧', '😨', '😰', '😥', '😢', '😭', '😱',
    '😖', '😣', '😞', '😓', '😩', '😫', '🥱', '😤', '😡', '😠'
  ],
  animals: [
    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
    '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔',
    '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺',
    '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟',
    '🦗', '🕷️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑'
  ],
  food: [
    '🍏', '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈',
    '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
    '🥬', '🥒', '🌶️', '🌽', '🥕', '🥔', '🍠', '🥐', '🍞', '🥖',
    '🥨', '🧀', '🥚', '🍳', '🥞', '🥓', '🥩', '🍗', '🍖', '🌭',
    '🍔', '🍟', '🍕', '🥪', '🥙', '🌮', '🌯', '🥗', '🥘', '🍝'
  ],
  objects: [
    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
    '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🥅', '⛳', '🏹', '🎣',
    '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂',
    '🏋️', '🤸', '🤺', '🤾', '🏌️', '🏇', '🧘', '🏄', '🏊', '🤽',
    '🚣', '🧗', '🚵', '🚴', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️'
  ],
  symbols: [
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
    '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
    '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐',
    '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳'
  ]
};

const categoryIcons = {
  recent: '🕐',
  smileys: '😀',
  animals: '🐶',
  food: '🍎',
  objects: '⚽',
  symbols: '❤️'
};

export const CompactEmojiPicker: React.FC<CompactEmojiPickerProps> = ({
  onEmojiSelect,
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof emojiCategories>('recent');
  const pickerRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    // Keep picker open for multiple selections
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current && 
        !pickerRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen]);

  // Clone children and add click handler
  const childrenWithProps = React.cloneElement(children, {
    ...children.props,
    onClick: (e: React.MouseEvent) => {
      e.stopPropagation();
      setIsOpen(!isOpen);
      // Call original onClick if exists
      const originalOnClick = children.props?.onClick;
      if (originalOnClick && typeof originalOnClick === 'function') {
        originalOnClick(e);
      }
    },
    onPress: () => {
      setIsOpen(!isOpen);
      // Call original onPress if exists
      const originalOnPress = children.props?.onPress;
      if (originalOnPress && typeof originalOnPress === 'function') {
        originalOnPress();
      }
    }
  });

  return (
    <div className="relative">
      <div ref={triggerRef}>
        {childrenWithProps}
      </div>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={pickerRef}
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            transition={{ duration: 0.15 }}
            className="absolute bottom-full mb-2 right-0 w-[320px] bg-custom-card border border-custom-border rounded-lg shadow-lg z-50"
          >
            <div className="flex flex-col h-[280px]">
              {/* Category tabs */}
              <div className="flex items-center gap-1 p-2 border-b border-custom-border">
                {(Object.keys(categoryIcons) as Array<keyof typeof categoryIcons>).map((category) => (
                  <Button
                    key={category}
                    isIconOnly
                    size="sm"
                    variant={selectedCategory === category ? "flat" : "light"}
                    className={selectedCategory === category ? "bg-custom-primary/20" : ""}
                    onPress={() => setSelectedCategory(category)}
                  >
                    <span className="text-base">{categoryIcons[category]}</span>
                  </Button>
                ))}
              </div>

              {/* Emoji grid */}
              <ScrollShadow className="flex-1 p-2">
                <div className="grid grid-cols-8 gap-1">
                  {emojiCategories[selectedCategory].map((emoji, index) => (
                    <button
                      key={`${emoji}-${index}`}
                      className="w-8 h-8 flex items-center justify-center rounded hover:bg-custom-border/20 transition-colors text-lg"
                      onClick={() => handleEmojiClick(emoji)}
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </ScrollShadow>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};