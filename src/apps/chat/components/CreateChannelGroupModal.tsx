import React, { useState, useMemo, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Input,
  Select,
  SelectItem,
  Divider,
  Chip,
  Checkbox,
  Avatar,
  Textarea
} from "@heroui/react";
import { TauriIcon as Icon } from "../../../components/TauriIcon";
import { mockUsers, mockTeams } from "../data/mockData";

interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
}

interface Team {
  id: string;
  name: string;
  members: string[];
}

interface CreateChannelGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateSuccess: (channelData: any) => void;
  type: 'channel' | 'group';
  isEditMode?: boolean;
  existingData?: {
    id: string;
    title: string;
    description: string;
    avatar: string;
    members: string[];
    isPublic: boolean;
    admins: string[];
  };
}

export const CreateChannelGroupModal: React.FC<CreateChannelGroupModalProps> = ({
  isOpen,
  onClose,
  onCreateSuccess,
  type,
  isEditMode = false,
  existingData
}) => {
  // State for form fields
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [accessType, setAccessType] = useState<"public" | "custom">("custom");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<string>("");
  const [admins, setAdmins] = useState<string[]>([]);
  const [uploadedAvatar, setUploadedAvatar] = useState<string | null>(null);
  const [isAvatarModalOpen, setIsAvatarModalOpen] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [position, setPosition] = useState({ x: 0.5, y: 0.5 });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Predefined avatars
  const avatarOptions = [
    { id: 1, url: `https://img.heroui.chat/image/avatar?w=200&h=200&u=101` },
    { id: 2, url: `https://img.heroui.chat/image/avatar?w=200&h=200&u=102` },
    { id: 3, url: `https://img.heroui.chat/image/avatar?w=200&h=200&u=103` },
    { id: 4, url: `https://img.heroui.chat/image/avatar?w=200&h=200&u=104` },
    { id: 5, url: `https://img.heroui.chat/image/avatar?w=200&h=200&u=105` },
  ];

  // Filter users based on search query
  const filteredUsers = useMemo(() => {
    if (!searchQuery.trim()) return [];
    
    const query = searchQuery.toLowerCase();
    return mockUsers.filter(
      user => 
        user.name.toLowerCase().includes(query) && 
        !selectedParticipants.includes(user.id)
    );
  }, [searchQuery, selectedParticipants]);

  // Handle user selection
  const handleUserSelect = (userId: string) => {
    setSelectedParticipants(prev => [...prev, userId]);
    setSearchQuery("");
  };

  // Handle user removal
  const handleUserRemove = (userId: string) => {
    setSelectedParticipants(prev => prev.filter(id => id !== userId));
  };

  // Handle team selection
  const handleTeamSelect = (teamId: string) => {
    setSelectedTeams(prev => 
      prev.includes(teamId) 
        ? prev.filter(id => id !== teamId) 
        : [...prev, teamId]
    );
  };

  // Handle admin assignment
  const handleAdminToggle = (userId: string) => {
    setAdmins(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setUploadedAvatar(event.target?.result as string);
        setIsAvatarModalOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle crop complete
  const handleCropComplete = () => {
    // In a real app, we would create a cropped image here
    // For this example, we'll just use the uploaded image
    setSelectedAvatar(uploadedAvatar || "");
    setIsAvatarModalOpen(false);
  };

  // Reset avatar modal on close
  const handleAvatarModalClose = () => {
    setIsAvatarModalOpen(false);
    setZoom(1);
    setPosition({ x: 0.5, y: 0.5 });
  };

  // Handle zoom change
  const handleZoomChange = (value: number) => {
    setZoom(value);
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!name.trim()) return;
    
    setIsProcessing(true);
    
    // Get all participants (individual users + team members)
    const allParticipantIds = [...selectedParticipants];
    
    // Add team members
    selectedTeams.forEach(teamId => {
      const team = mockTeams.find(t => t.id === teamId);
      if (team) {
        team.members.forEach(memberId => {
          if (!allParticipantIds.includes(memberId)) {
            allParticipantIds.push(memberId);
          }
        });
      }
    });
    
    const data = {
      id: isEditMode && existingData ? existingData.id : `${type}-${Date.now()}`,
      title: name,
      description: description,
      type: type,
      isPublic: accessType === 'public',
      members: allParticipantIds,
      avatar: selectedAvatar,
      createdAt: isEditMode && existingData ? existingData.createdAt : new Date(),
      lastMessage: isEditMode && existingData ? existingData.lastMessage : null,
      unreadCount: isEditMode && existingData ? existingData.unreadCount : 0,
      isPinned: isEditMode && existingData ? existingData.isPinned : false,
      admins: admins,
    };
    
    // Simulate API call
    setTimeout(() => {
      onCreateSuccess(data);
      setIsProcessing(false);
      resetForm();
      onClose();
    }, 1000);
  };
  
  // Initialize form with existing data if in edit mode
  React.useEffect(() => {
    if (isEditMode && existingData) {
      setName(existingData.title);
      setDescription(existingData.description || "");
      setSelectedAvatar(existingData.avatar);
      setAccessType(existingData.isPublic ? "public" : "custom");
      setSelectedParticipants(existingData.members || []);
      setAdmins(existingData.admins || []);
      
      // Filter out team members that are already in selected participants
      // In a real app, you'd need to determine which teams are selected based on members
    }
  }, [isEditMode, existingData]);

  const resetForm = () => {
    setName("");
    setDescription("");
    setAccessType("custom");
    setSearchQuery("");
    setSelectedParticipants([]);
    setSelectedTeams([]);
    setSelectedAvatar("");
    setAdmins([]);
  };
  
  const headerTitle = isEditMode
    ? `Edit ${type === 'channel' ? 'Channel' : 'Group'}`
    : `Create New ${type === 'channel' ? 'Channel' : 'Group'}`;
  
  return (
    <>
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        placement="center"
        size="2xl"
        classNames={{
          base: "bg-[#1A1D2B] border border-[#2A2D3C] max-h-[90vh] overflow-y-auto",
          header: "border-b border-[#2A2D3C]",
          footer: "border-t border-[#2A2D3C]",
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="text-white flex items-center">
                <Icon 
                  icon={type === 'channel' ? "lucide:megaphone" : "lucide:users"} 
                  className="text-primary mr-2" 
                />
                {headerTitle}
              </ModalHeader>
              <ModalBody className="py-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-white font-medium mb-3">Avatar</h3>
                    <div className="flex flex-col items-center justify-center">
                      <div className="relative mb-4">
                        <div 
                          className="w-24 h-24 rounded-full border-2 border-[#2A2D3C] overflow-hidden bg-[#12141F] flex items-center justify-center"
                        >
                          {selectedAvatar ? (
                            <img 
                              src={selectedAvatar} 
                              alt="Selected avatar" 
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Icon 
                              icon={type === 'channel' ? "lucide:megaphone" : "lucide:users"} 
                              className="text-primary text-3xl" 
                            />
                          )}
                        </div>
                        <Button 
                          isIconOnly 
                          color="primary" 
                          size="sm" 
                          className="absolute bottom-0 right-0"
                          onPress={triggerFileInput}
                        >
                          <Icon icon="lucide:edit" />
                        </Button>
                      </div>
                      <input 
                        type="file" 
                        accept="image/*" 
                        className="hidden" 
                        ref={fileInputRef} 
                        onChange={handleFileChange}
                      />
                      <Button 
                        variant="flat" 
                        color="primary" 
                        className="w-full"
                        onPress={triggerFileInput}
                        startContent={<Icon icon="lucide:upload" />}
                      >
                        Upload Avatar
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <Input
                      label={type === 'channel' ? "Channel Name" : "Group Name"}
                      placeholder={type === 'channel' ? "Enter channel name" : "Enter group name"}
                      value={name}
                      onValueChange={setName}
                      variant="bordered"
                      classNames={{
                        inputWrapper: "bg-[#12141F] border-[#2A2D3C]",
                        label: "text-white",
                        input: "text-white"
                      }}
                    />
                  </div>
                  
                  <div>
                    <Textarea
                      label="Description (optional)"
                      placeholder={`Describe the ${type}`}
                      value={description}
                      onValueChange={setDescription}
                      variant="bordered"
                      classNames={{
                        inputWrapper: "bg-[#12141F] border-[#2A2D3C]",
                        label: "text-white",
                        input: "text-white"
                      }}
                    />
                  </div>
                  
                  <Divider className="bg-[#2A2D3C]" />
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-white font-medium mb-4">Members</h3>
                      <Select
                        label={`${type === 'channel' ? "Channel" : "Group"} Access`}
                        selectedKeys={[accessType]}
                        onSelectionChange={(keys) => {
                          const selectedKey = Array.from(keys)[0];
                          if (selectedKey === "public" || selectedKey === "custom") {
                            setAccessType(selectedKey);
                          }
                        }}
                        variant="bordered"
                        classNames={{
                          trigger: "bg-[#12141F] border-[#2A2D3C]",
                          popoverContent: "bg-[#1A1D2B] border-[#2A2D3C]",
                          label: "text-white"
                        }}
                      >
                        <SelectItem key="public">Public (All users)</SelectItem>
                        <SelectItem key="custom">Custom (Selected users & teams)</SelectItem>
                      </Select>
                    </div>
                    
                    {accessType === "custom" && (
                      <>
                        <Divider className="bg-[#2A2D3C]" />
                        
                        <div>
                          <h3 className="text-white font-medium mb-2">Users with Access</h3>
                          <Input
                            placeholder="Search users..."
                            value={searchQuery}
                            onValueChange={setSearchQuery}
                            variant="bordered"
                            startContent={<Icon icon="lucide:search" className="text-[#7D8597]" />}
                            classNames={{
                              inputWrapper: "bg-[#12141F] border-[#2A2D3C]",
                              input: "text-white"
                            }}
                          />
                          
                          {searchQuery && filteredUsers.length > 0 && (
                            <div className="mt-2 bg-[#12141F] border border-[#2A2D3C] rounded-lg max-h-[160px] overflow-y-auto">
                              {filteredUsers.map(user => (
                                <div 
                                  key={user.id}
                                  className="flex items-center justify-between p-2 hover:bg-[#2A2D3C]/30 cursor-pointer"
                                  onClick={() => handleUserSelect(user.id)}
                                >
                                  <div className="flex items-center">
                                    <Avatar src={user.avatar} size="sm" className="mr-2" />
                                    <span className="text-white text-sm">{user.name}</span>
                                  </div>
                                  <Button 
                                    isIconOnly 
                                    size="sm" 
                                    variant="flat" 
                                    className="text-[#7D8597]"
                                  >
                                    <Icon icon="lucide:plus" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                          
                          {selectedParticipants.length > 0 && (
                            <div className="mt-3">
                              <div className="mb-2 flex justify-between items-center">
                                <span className="text-white text-sm">Selected Users</span>
                                {type === 'channel' && (
                                  <span className="text-[#7D8597] text-xs">Toggle admin rights</span>
                                )}
                              </div>
                              <div className="space-y-2">
                                {selectedParticipants.map(userId => {
                                  const user = mockUsers.find(u => u.id === userId);
                                  if (!user) return null;
                                  
                                  return (
                                    <div 
                                      key={user.id}
                                      className="flex items-center justify-between p-2 bg-[#12141F] rounded-lg"
                                    >
                                      <div className="flex items-center">
                                        <Avatar src={user.avatar} size="sm" className="mr-2" />
                                        <span className="text-white text-sm">{user.name}</span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        {type === 'channel' && (
                                          <Checkbox
                                            isSelected={admins.includes(user.id)}
                                            onValueChange={() => handleAdminToggle(user.id)}
                                            color="primary"
                                            aria-label="Toggle admin"
                                          >
                                            <span className="text-xs text-[#C0C4CC]">Admin</span>
                                          </Checkbox>
                                        )}
                                        <Button 
                                          isIconOnly 
                                          size="sm" 
                                          variant="flat" 
                                          className="text-[#7D8597]"
                                          onPress={() => handleUserRemove(user.id)}
                                        >
                                          <Icon icon="lucide:x" />
                                        </Button>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <Divider className="bg-[#2A2D3C]" />
                        
                        <div>
                          <h3 className="text-white font-medium mb-2">Teams with Access</h3>
                          <div className="space-y-2 mt-2">
                            {mockTeams.map(team => (
                              <div 
                                key={team.id}
                                className="flex items-center justify-between p-2 bg-[#12141F] rounded-lg"
                              >
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-md bg-[#1A1D2B] flex items-center justify-center mr-2">
                                    <Icon icon="lucide:users" className="text-primary" />
                                  </div>
                                  <div>
                                    <div className="text-white text-sm">{team.name}</div>
                                    <div className="text-[#7D8597] text-xs">{team.members.length} members</div>
                                  </div>
                                </div>
                                <Checkbox
                                  isSelected={selectedTeams.includes(team.id)}
                                  onValueChange={() => handleTeamSelect(team.id)}
                                  color="primary"
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button 
                  variant="light" 
                  onPress={() => {
                    resetForm();
                    onClose();
                  }}
                  className="text-[#C0C4CC]"
                >
                  Cancel
                </Button>
                <Button 
                  color="primary" 
                  onPress={handleSubmit}
                  isDisabled={!name.trim() || (accessType === "custom" && selectedParticipants.length === 0 && selectedTeams.length === 0)}
                  isLoading={isProcessing}
                >
                  {isProcessing ? 
                    (isEditMode ? 'Saving...' : 'Creating...') : 
                    (isEditMode ? 'Save Changes' : `Create ${type === 'channel' ? 'Channel' : 'Group'}`)
                  }
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      <Modal 
        isOpen={isAvatarModalOpen} 
        onClose={handleAvatarModalClose}
        placement="center"
        size="lg"
        classNames={{
          base: "bg-[#1A1D2B] border border-[#2A2D3C] ",
          header: "border-b border-[#2A2D3C]",
          footer: "border-t border-[#2A2D3C]",
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="text-white flex items-center">
                <Icon icon="lucide:crop" className="text-primary mr-2" />
                Adjust Avatar
              </ModalHeader>
              <ModalBody className="py-6">
                <div className="space-y-4">
                  <div className="relative flex flex-col items-center">
                    <div className="w-56 h-56 rounded-full border-2 border-primary overflow-hidden relative bg-[#12141F]">
                      {uploadedAvatar && (
                        <div 
                          className="absolute w-full h-full"
                          style={{
                            backgroundImage: `url(${uploadedAvatar})`,
                            backgroundSize: `${zoom * 100}%`,
                            backgroundPosition: `${position.x * 100}% ${position.y * 100}%`,
                            backgroundRepeat: 'no-repeat'
                          }}
                        />
                      )}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="w-full h-full rounded-full border-4 border-white/30" />
                      </div>
                    </div>
                    
                    <div className="w-full mt-6 px-4">
                      <div className="flex items-center mb-2">
                        <Icon icon="lucide:zoom-in" className="text-white mr-2" />
                        <span className="text-white text-sm">Zoom</span>
                      </div>
                      <input
                        type="range"
                        min="1"
                        max="3"
                        step="0.1"
                        value={zoom}
                        onChange={(e) => handleZoomChange(parseFloat(e.target.value))}
                        className="w-full accent-primary bg-[#12141F] h-2 rounded-lg appearance-none cursor-pointer"
                      />
                    </div>
                    
                    <div className="w-full mt-4 px-4">
                      <div className="text-white text-sm mb-2">Position</div>
                      <div className="grid grid-cols-3 gap-2">
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0, y: 0 })}
                        >
                          <Icon icon="lucide:arrow-up-left" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0.5, y: 0 })}
                        >
                          <Icon icon="lucide:arrow-up" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 1, y: 0 })}
                        >
                          <Icon icon="lucide:arrow-up-right" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0, y: 0.5 })}
                        >
                          <Icon icon="lucide:arrow-left" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0.5, y: 0.5 })}
                        >
                          <Icon icon="lucide:maximize" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 1, y: 0.5 })}
                        >
                          <Icon icon="lucide:arrow-right" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0, y: 1 })}
                        >
                          <Icon icon="lucide:arrow-down-left" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 0.5, y: 1 })}
                        >
                          <Icon icon="lucide:arrow-down" className="text-white" />
                        </Button>
                        <Button 
                          variant="flat" 
                          size="sm" 
                          className="bg-[#12141F]"
                          onPress={() => setPosition({ x: 1, y: 1 })}
                        >
                          <Icon icon="lucide:arrow-down-right" className="text-white" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button 
                  variant="light" 
                  onPress={handleAvatarModalClose}
                  className="text-[#C0C4CC]"
                >
                  Cancel
                </Button>
                <Button 
                  color="primary" 
                  onPress={handleCropComplete}
                >
                  Apply
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};