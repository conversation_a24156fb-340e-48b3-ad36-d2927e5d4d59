import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { <PERSON>are<PERSON>, Button, Tooltip } from "@heroui/react";
import { TauriIcon as Icon } from "../../../components/TauriIcon";
import type { ChatMessage, ChatType } from "../types";
import { motion, AnimatePresence } from "framer-motion";
import { CompactEmojiPicker } from "./CompactEmojiPicker";

interface MessageInputProps {
  onSendMessage: (content: string, attachments?: any[]) => void;
  replyTo: ChatMessage | null;
  onCancelReply: () => void;
  conversationType: ChatType;
  chatId?: string;
  chatState?: {
    saveDraftMessage: (chatId: string, message: string) => void;
    getDraftMessage: (chatId: string) => string;
  };
  onTyping?: () => void;
}

export interface MessageInputRef {
  focus: () => void;
}

const MAX_MESSAGE_LENGTH = 4000; // Maximum characters allowed

export const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(({
  onSendMessage,
  replyTo,
  onCancelReply,
  conversationType,
  chatId,
  chatState,
  onTyping
}, ref) => {
  const [message, setMessage] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const timerRef = useRef<number | null>(null);
  const previousChatIdRef = useRef<string | undefined>(chatId);
  
  // Expose focus method to parent component
  useImperativeHandle(ref, () => ({
    focus: () => {
      textareaRef.current?.focus();
    }
  }));
  
  // Load draft message when chat changes - but preserve existing text
  useEffect(() => {
    if (chatId && chatState) {
      // If we're switching chats and have unsaved text, save it first
      if (previousChatIdRef.current && previousChatIdRef.current !== chatId && message.trim()) {
        chatState.saveDraftMessage(previousChatIdRef.current, message);
      }
      
      // Load draft for the new chat
      const draft = chatState.getDraftMessage(chatId);
      setMessage(draft || "");
      
      // Update the ref
      previousChatIdRef.current = chatId;
    }
  }, [chatId]);
  
  // Save draft message when it changes - with better debouncing
  useEffect(() => {
    if (chatId && chatState && message.trim()) {
      const timer = setTimeout(() => {
        chatState.saveDraftMessage(chatId, message);
      }, 300); // Shorter debounce for better UX
      
      return () => clearTimeout(timer);
    }
  }, [message, chatId, chatState]);

  // Save draft message when component unmounts to prevent data loss
  useEffect(() => {
    return () => {
      if (chatId && chatState && message.trim()) {
        chatState.saveDraftMessage(chatId, message);
      }
    };
  }, []);
  
  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message);
      setMessage("");
      // Clear draft after sending
      if (chatId && chatState) {
        chatState.saveDraftMessage(chatId, "");
      }
    }
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Send on Enter (without modifiers)
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey && !e.altKey) {
      e.preventDefault();
      handleSend();
    }
    // New line on Shift+Enter
    else if (e.key === 'Enter' && e.shiftKey) {
      // Let the default behavior happen (new line)
    }
  };
  
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    // In a real app, you would upload these files to a server
    // For this demo, we'll just simulate attachments
    const attachments = Array.from(files).map(file => ({
      type: file.type.startsWith('image/') ? 'image' : 'file',
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size
    }));
    
    onSendMessage(message, attachments);
    setMessage("");
    // Clear draft after sending
    if (chatId && chatState) {
      chatState.saveDraftMessage(chatId, "");
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      setIsRecording(false);
      
      // In a real app, you would process the audio recording here
      // For this demo, we'll just simulate sending a voice message
      onSendMessage("🎤 Voice message");
      setRecordingTime(0);
    } else {
      // Start recording
      setIsRecording(true);
      timerRef.current = window.setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
  };
  
  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const handleEmojiSelect = (emoji: string) => {
    setMessage(prev => {
      const newMessage = prev + emoji;
      // Respect character limit
      return newMessage.length <= MAX_MESSAGE_LENGTH ? newMessage : prev;
    });
  };

  const handleMessageChange = (value: string) => {
    // Respect character limit
    if (value.length <= MAX_MESSAGE_LENGTH) {
      setMessage(value);
      onTyping?.();
    }
  };

  const getCharacterCountColor = () => {
    const remaining = MAX_MESSAGE_LENGTH - message.length;
    if (remaining < 100) return "text-danger";
    if (remaining < 300) return "text-warning";
    return "text-custom-muted";
  };
  
  return (
    <div className="p-3 border-t border-custom-border">
      <AnimatePresence>
        {replyTo && (
          <motion.div 
            className="flex items-center bg-custom-sidebar rounded-lg p-2 mb-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <Icon icon="lucide:reply" className="text-custom-primary mr-2" />
                <span className="text-custom-primary text-xs font-medium">
                  Reply to {replyTo?.senderName}
                </span>
              </div>
              <p className="text-custom-text text-xs truncate">{replyTo?.content}</p>
            </div>
            <Button 
              isIconOnly 
              variant="light" 
              size="sm" 
              className="text-custom-muted"
              onPress={onCancelReply}
            >
              <Icon icon="lucide:x" className="text-xs" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {isRecording ? (
        <div className="flex items-center bg-custom-sidebar rounded-lg p-3">
          <div className="flex-1 flex items-center">
            <div className="w-3 h-3 rounded-full bg-danger animate-pulse mr-3"></div>
            <span className="text-custom-text">Recording... {formatRecordingTime(recordingTime)}</span>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="light" 
              color="danger" 
              size="sm" 
              startContent={<Icon icon="lucide:trash-2" />}
              onPress={() => {
                if (timerRef.current) {
                  clearInterval(timerRef.current);
                  timerRef.current = null;
                }
                setIsRecording(false);
                setRecordingTime(0);
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="solid" 
              color="primary" 
              size="sm" 
              startContent={<Icon icon="lucide:send" />}
              onPress={toggleRecording}
            >
              Send
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-end gap-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            className="hidden"
            multiple
          />
          
          <Tooltip content="Attach files">
            <Button 
              isIconOnly 
              variant="light" 
              className="text-custom-text"
              onPress={() => fileInputRef.current?.click()}
            >
              <Icon icon="lucide:paperclip" />
            </Button>
          </Tooltip>
          
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              placeholder={conversationType === 'channel' ? "Broadcast a message..." : "Type a message..."}
              value={message}
              onValueChange={handleMessageChange}
              onKeyDown={handleKeyDown}
              classNames={{
                base: "max-h-[200px]",
                inputWrapper: "bg-custom-sidebar border-custom-border min-h-[44px] py-2 px-3 data-[hover=true]:bg-custom-sidebar",
                input: "resize-none scrollbar-thin scrollbar-thumb-custom-border scrollbar-track-transparent",
                innerWrapper: "gap-0",
              }}
              radius="lg"
              variant="bordered"
              minRows={1}
              maxRows={8}
            />
            <div className="absolute right-2 bottom-2 flex items-center gap-1">
              <CompactEmojiPicker onEmojiSelect={handleEmojiSelect}>
                <Button
                  isIconOnly
                  variant="light"
                  size="sm"
                  className="text-custom-text hover:bg-custom-border/20"
                  title="Emoji"
                >
                  <Icon icon="lucide:smile" />
                </Button>
              </CompactEmojiPicker>
              {message.trim() && (
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0, opacity: 0 }}
                  transition={{ duration: 0.1 }}
                >
                  <Button
                    isIconOnly
                    color="primary"
                    size="sm"
                    className="ml-1"
                    onPress={handleSend}
                  >
                    <Icon icon="lucide:send" />
                  </Button>
                </motion.div>
              )}
            </div>
            
            {/* Character Counter */}
            {message.length > 0 && (
              <div className={`absolute right-2 top-1 text-xs ${getCharacterCountColor()}`}>
                {MAX_MESSAGE_LENGTH - message.length}
              </div>
            )}
            
            {/* Character Limit Progress Bar */}
            {message.length > MAX_MESSAGE_LENGTH * 0.8 && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5">
                <div 
                  className={`h-full transition-all duration-200 ${
                    message.length >= MAX_MESSAGE_LENGTH ? 'bg-danger' : 
                    message.length > MAX_MESSAGE_LENGTH * 0.9 ? 'bg-warning' : 'bg-primary'
                  }`}
                  style={{ width: `${(message.length / MAX_MESSAGE_LENGTH) * 100}%` }}
                />
              </div>
            )}
          </div>
          
          {!message.trim() && (
            <Tooltip content="Voice message">
              <Button
                isIconOnly
                variant="light"
                className="text-custom-text hover:bg-custom-border/20"
                onPress={toggleRecording}
              >
                <Icon icon="lucide:mic" />
              </Button>
            </Tooltip>
          )}
        </div>
      )}

    </div>
  );
});

MessageInput.displayName = 'MessageInput';