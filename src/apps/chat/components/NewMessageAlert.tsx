import React from "react";
import { But<PERSON> } from "@heroui/react";
import { TauriIcon as Icon } from "../../../components/TauriIcon";
import { motion } from "framer-motion";

interface NewMessageAlertProps {
  onClick: () => void;
}

export const NewMessageAlert: React.FC<NewMessageAlertProps> = ({ onClick }) => {
  return (
    <motion.div 
      className="absolute bottom-20 left-0 right-0 flex justify-center"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
    >
      <Button
        color="primary"
        variant="solid"
        size="sm"
        className="shadow-lg"
        startContent={<Icon icon="lucide:chevron-down" />}
        onPress={onClick}
      >
        New messages
      </Button>
    </motion.div>
  );
};