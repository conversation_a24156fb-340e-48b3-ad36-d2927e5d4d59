import React, { useEffect, useRef, useState } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '@heroui/react';
import type { PopoverProps } from '@heroui/react';

interface SafePopoverProps extends Omit<PopoverProps, 'children' | 'content'> {
  children: React.ReactNode;
  content: React.ReactNode;
  contentClassName?: string;
}

/**
 * SafePopover is a wrapper around HeroUI's Popover that handles ResizeObserver errors
 * by ensuring the component is properly mounted before attempting to observe elements.
 */
export const SafePopover: React.FC<SafePopoverProps> = ({
  children,
  content,
  contentClassName,
  isOpen,
  onOpenChange,
  ...props
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Delay mounting to ensure DOM is ready
    const mountTimer = setTimeout(() => {
      setIsMounted(true);
    }, 50);

    return () => {
      clearTimeout(mountTimer);
      setIsMounted(false);
    };
  }, []);

  useEffect(() => {
    // Only open the popover after component is mounted
    if (isMounted && isOpen) {
      // Small delay to ensure DOM elements are ready
      const openTimer = setTimeout(() => {
        setInternalIsOpen(true);
      }, 100);

      return () => clearTimeout(openTimer);
    } else {
      setInternalIsOpen(false);
    }
  }, [isMounted, isOpen]);

  const handleOpenChange = (open: boolean) => {
    if (!isMounted) return;
    
    // Prevent opening if component is not fully mounted
    if (open && !isMounted) {
      return;
    }

    onOpenChange?.(open);
  };

  // Override ResizeObserver to add error handling
  useEffect(() => {
    if (!window.ResizeObserver) return;

    const OriginalResizeObserver = window.ResizeObserver;
    
    // Create a patched ResizeObserver that handles errors gracefully
    class SafeResizeObserver extends OriginalResizeObserver {
      constructor(callback: ResizeObserverCallback) {
        super((entries, observer) => {
          try {
            callback(entries, observer);
          } catch (error) {
            console.warn('ResizeObserver error caught:', error);
          }
        });
      }

      observe(target: Element, options?: ResizeObserverOptions) {
        try {
          if (target && target instanceof Element && document.contains(target)) {
            super.observe(target, options);
          }
        } catch (error) {
          console.warn('ResizeObserver.observe error:', error);
        }
      }

      unobserve(target: Element) {
        try {
          if (target && target instanceof Element) {
            super.unobserve(target);
          }
        } catch (error) {
          console.warn('ResizeObserver.unobserve error:', error);
        }
      }

      disconnect() {
        try {
          super.disconnect();
        } catch (error) {
          console.warn('ResizeObserver.disconnect error:', error);
        }
      }
    }

    // Temporarily replace ResizeObserver
    (window as any).ResizeObserver = SafeResizeObserver;

    return () => {
      // Restore original ResizeObserver
      (window as any).ResizeObserver = OriginalResizeObserver;
    };
  }, []);

  if (!isMounted) {
    // Return just the trigger element while mounting
    return <>{children}</>;
  }

  return (
    <Popover 
      isOpen={internalIsOpen}
      onOpenChange={handleOpenChange}
      {...props}
    >
      <PopoverTrigger>
        {children}
      </PopoverTrigger>
      <PopoverContent className={contentClassName}>
        <div ref={contentRef}>
          {content}
        </div>
      </PopoverContent>
    </Popover>
  );
};