import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MessageInput } from '../MessageInput';
import type { ChatMessage } from '../../types';

// Mock TauriIcon component
vi.mock('../../../../components/TauriIcon', () => ({
  TauriIcon: ({ icon, className }: { icon: string; className?: string }) => (
    <span data-testid={`icon-${icon}`} className={className} />
  ),
}));

// Mock CompactEmojiPicker
vi.mock('../CompactEmojiPicker', () => ({
  CompactEmojiPicker: ({ children, onEmojiSelect }: { children: React.ReactNode; onEmojiSelect: (emoji: string) => void }) => (
    <div data-testid="emoji-picker" onClick={() => onEmojiSelect('😀')}>
      {children}
    </div>
  ),
}));

describe('MessageInput', () => {
  const mockOnSendMessage = vi.fn();
  const mockOnCancelReply = vi.fn();
  const mockOnTyping = vi.fn();
  const mockChatState = {
    saveDraftMessage: vi.fn(),
    getDraftMessage: vi.fn(() => ''),
  };

  const defaultProps = {
    onSendMessage: mockOnSendMessage,
    replyTo: null,
    onCancelReply: mockOnCancelReply,
    conversationType: 'direct' as const,
    chatId: 'test-chat-1',
    chatState: mockChatState,
    onTyping: mockOnTyping,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render message input correctly', () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    expect(textarea).toBeInTheDocument();
  });

  it('should respect character limit', async () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // Type a message within limit
    const shortMessage = 'Hello world!';
    fireEvent.change(textarea, { target: { value: shortMessage } });
    expect(textarea).toHaveValue(shortMessage);
    
    // Try to type a message exceeding limit
    const longMessage = 'a'.repeat(4001); // Exceeds MAX_MESSAGE_LENGTH (4000)
    fireEvent.change(textarea, { target: { value: longMessage } });
    
    // Should still have the short message, not the long one
    expect(textarea).toHaveValue(shortMessage);
  });

  it('should show character counter when typing', async () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // Type a message
    fireEvent.change(textarea, { target: { value: 'Hello' } });
    
    // Should show character counter
    await waitFor(() => {
      expect(screen.getByText('3995')).toBeInTheDocument(); // 4000 - 5
    });
  });

  it('should save draft message when typing', async () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // Type a message
    fireEvent.change(textarea, { target: { value: 'Hello draft' } });
    
    // Wait for debounced save
    await waitFor(() => {
      expect(mockChatState.saveDraftMessage).toHaveBeenCalledWith('test-chat-1', 'Hello draft');
    }, { timeout: 400 });
  });

  it('should load draft message when chat changes', () => {
    mockChatState.getDraftMessage.mockReturnValue('Saved draft message');
    
    render(<MessageInput {...defaultProps} />);
    
    expect(mockChatState.getDraftMessage).toHaveBeenCalledWith('test-chat-1');
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    expect(textarea).toHaveValue('Saved draft message');
  });

  it('should handle emoji selection', async () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // First type some text
    fireEvent.change(textarea, { target: { value: 'Hello ' } });
    
    // Click emoji picker
    const emojiPicker = screen.getByTestId('emoji-picker');
    fireEvent.click(emojiPicker);
    
    // Should append emoji to existing text
    expect(textarea).toHaveValue('Hello 😀');
  });

  it('should send message on Enter key', () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // Type a message
    fireEvent.change(textarea, { target: { value: 'Test message' } });
    
    // Press Enter
    fireEvent.keyDown(textarea, { key: 'Enter' });
    
    // Should call onSendMessage
    expect(mockOnSendMessage).toHaveBeenCalledWith('Test message');
    
    // Should clear the input
    expect(textarea).toHaveValue('');
  });

  it('should add new line on Shift+Enter', () => {
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    
    // Type a message
    fireEvent.change(textarea, { target: { value: 'Line 1' } });
    
    // Press Shift+Enter (should not send message)
    fireEvent.keyDown(textarea, { key: 'Enter', shiftKey: true });
    
    // Should not call onSendMessage
    expect(mockOnSendMessage).not.toHaveBeenCalled();
    
    // Should still have the message
    expect(textarea).toHaveValue('Line 1');
  });

  it('should display reply banner when replying to a message', () => {
    const replyTo: ChatMessage = {
      id: 'msg-1',
      senderId: 'user-1',
      senderName: 'John Doe',
      content: 'Original message',
      timestamp: new Date(),
      status: 'read',
      isOutgoing: false,
    };

    render(<MessageInput {...defaultProps} replyTo={replyTo} />);
    
    expect(screen.getByText('Reply to John Doe')).toBeInTheDocument();
    expect(screen.getByText('Original message')).toBeInTheDocument();
  });
});