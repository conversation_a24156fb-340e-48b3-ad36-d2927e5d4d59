// ... existing code ...

export interface ChatConversation {
        id: string;
        title: string;
        type: 'private' | 'group' | 'channel';
        avatar?: string;
        lastMessage?: ChatMessage;
        unreadCount: number;
        isPinned: boolean;
        isOnline?: boolean;
        lastSeen?: Date;
        isTyping?: boolean;
        description?: string;
        members?: string[];
        // Add additional properties for channels/groups
        isPublic?: boolean;
        createdAt?: Date;
        createdBy?: string;
        admins?: string[];
        isHighlighted?: boolean;
      }
      
      // ... rest of existing types ...