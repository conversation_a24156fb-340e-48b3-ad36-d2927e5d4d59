import { useRef, useCallback, useEffect } from 'react';

interface ChatState {
  scrollPosition: number;
  draftMessage: string;
}

export const useChatState = () => {
  const chatStatesRef = useRef<Map<string, ChatState>>(new Map());

  // Save scroll position for a chat
  const saveScrollPosition = useCallback((chatId: string, position: number) => {
    const currentState = chatStatesRef.current.get(chatId) || { scrollPosition: 0, draftMessage: '' };
    chatStatesRef.current.set(chatId, {
      ...currentState,
      scrollPosition: position,
    });
  }, []);

  // Get scroll position for a chat
  const getScrollPosition = useCallback((chatId: string): number => {
    return chatStatesRef.current.get(chatId)?.scrollPosition || 0;
  }, []);

  // Save draft message for a chat
  const saveDraftMessage = useCallback((chatId: string, message: string) => {
    const currentState = chatStatesRef.current.get(chatId) || { scrollPosition: 0, draftMessage: '' };
    chatStatesRef.current.set(chatId, {
      ...currentState,
      draftMessage: message,
    });
  }, []);

  // Get draft message for a chat
  const getDraftMessage = useCallback((chatId: string): string => {
    return chatStatesRef.current.get(chatId)?.draftMessage || '';
  }, []);

  // Clear state for a chat
  const clearChatState = useCallback((chatId: string) => {
    chatStatesRef.current.delete(chatId);
  }, []);

  return {
    saveScrollPosition,
    getScrollPosition,
    saveDraftMessage,
    getDraftMessage,
    clearChatState,
  };
};

// Hook to restore scroll position
export const useRestoreScroll = (
  chatId: string | undefined,
  scrollContainerRef: React.RefObject<HTMLDivElement>,
  getScrollPosition: (chatId: string) => number
) => {
  useEffect(() => {
    if (!chatId || !scrollContainerRef.current) return;

    const savedPosition = getScrollPosition(chatId);
    if (savedPosition > 0) {
      // Restore scroll position after a small delay to ensure content is rendered
      const timer = setTimeout(() => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop = savedPosition;
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [chatId, getScrollPosition, scrollContainerRef]);
};