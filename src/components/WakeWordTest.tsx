import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

interface WakeWordTestProps {
  className?: string;
}

interface WakeWordEvent {
  payload: string;
}

const WakeWordTest: React.FC<WakeWordTestProps> = ({ className = "" }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [detectionLogs, setDetectionLogs] = useState<string[]>([]);
  const [lastDetection, setLastDetection] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Check initial status
  useEffect(() => {
    checkStatus();
  }, []);

  // Listen for wake word events
  useEffect(() => {
    const setupListener = async () => {
      const unlisten = await listen<WakeWordEvent>('wake-word-detected', (event) => {
        const detectionMessage = event.payload;
        console.log('🎯 Wake word detected!', detectionMessage);
        
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] Wake word detected: ${detectionMessage}`;
        
        setDetectionLogs(prev => [logEntry, ...prev.slice(0, 9)]); // Keep last 10 logs
        setLastDetection(timestamp);
        
        // You can add more actions here:
        // - Show notification
        // - Play sound
        // - Trigger app functionality
      });

      return unlisten;
    };

    setupListener();
  }, []);

  const checkStatus = async () => {
    try {
      const status = await invoke<boolean>('get_wake_word_status');
      setIsRunning(status);
    } catch (error) {
      console.error('Failed to check wake word status:', error);
    }
  };

  const startDetection = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      const result = await invoke<string>('start_wake_word_detection');
      console.log('Wake word detection started:', result);
      setIsRunning(true);
      setDetectionLogs(prev => [`[${new Date().toLocaleTimeString()}] Started wake word detection`, ...prev]);
    } catch (error) {
      console.error('Failed to start wake word detection:', error);
      setDetectionLogs(prev => [`[${new Date().toLocaleTimeString()}] Failed to start: ${error}`, ...prev]);
    } finally {
      setLoading(false);
    }
  };

  const stopDetection = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      const result = await invoke<string>('stop_wake_word_detection');
      console.log('Wake word detection stopped:', result);
      setIsRunning(false);
      setDetectionLogs(prev => [`[${new Date().toLocaleTimeString()}] Stopped wake word detection`, ...prev]);
    } catch (error) {
      console.error('Failed to stop wake word detection:', error);
      setDetectionLogs(prev => [`[${new Date().toLocaleTimeString()}] Failed to stop: ${error}`, ...prev]);
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = () => {
    setDetectionLogs([]);
    setLastDetection(null);
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Wake Word Detection Test
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600">
            {isRunning ? 'Running' : 'Stopped'}
          </span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex space-x-3 mb-4">
        <button
          onClick={startDetection}
          disabled={loading || isRunning}
          className={`px-4 py-2 rounded-md text-white font-medium transition-colors ${
            loading || isRunning 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {loading ? 'Starting...' : 'Start Detection'}
        </button>
        
        <button
          onClick={stopDetection}
          disabled={loading || !isRunning}
          className={`px-4 py-2 rounded-md text-white font-medium transition-colors ${
            loading || !isRunning 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-red-600 hover:bg-red-700'
          }`}
        >
          {loading ? 'Stopping...' : 'Stop Detection'}
        </button>

        <button
          onClick={clearLogs}
          className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Clear Logs
        </button>

        <button
          onClick={checkStatus}
          className="px-4 py-2 rounded-md border border-blue-300 text-blue-700 hover:bg-blue-50 transition-colors"
        >
          Refresh Status
        </button>
      </div>

      {/* Last Detection */}
      {lastDetection && (
        <div className="mb-4 p-3 bg-green-50 border-l-4 border-green-400 rounded">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400 text-lg">🎯</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">
                <strong>Last Detection:</strong> {lastDetection}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="text-blue-400 text-lg">ℹ️</span>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              <strong>Instructions:</strong> Start the detection and say "Team Bye" clearly into your microphone.
              The system will listen for the wake word and log detections below.
            </p>
          </div>
        </div>
      </div>

      {/* Detection Logs */}
      <div className="border rounded-md">
        <div className="bg-gray-50 px-4 py-2 border-b">
          <h3 className="text-sm font-medium text-gray-700">
            Detection Logs ({detectionLogs.length})
          </h3>
        </div>
        <div className="max-h-64 overflow-y-auto">
          {detectionLogs.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">
              No detections yet. Start detection and say "Team Bye" to test.
            </div>
          ) : (
            <div className="p-2">
              {detectionLogs.map((log, index) => (
                <div
                  key={index}
                  className={`mb-1 p-2 rounded text-xs font-mono ${
                    log.includes('detected:') 
                      ? 'bg-green-100 text-green-800' 
                      : log.includes('Failed') 
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WakeWordTest;