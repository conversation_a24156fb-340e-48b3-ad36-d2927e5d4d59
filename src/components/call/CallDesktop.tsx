import React, { useEffect } from "react";
import { useToastHelpers } from "../../context/ToastContext";
import { ToastContainer } from "../ui/Toast";

// Import hooks
import { useCallState } from "./hooks/useCallState";
import { useCallActions } from "./hooks/useCallActions";
import { useCallEvents } from "./hooks/useCallEvents";
import { useTeamMembers } from "./hooks/useTeamMembers";

// Import components
import { CallLoadingContent } from "./components/CallLoadingContent";
import { CallRingingContent } from "./components/CallRingingContent";
import { CallConnectedContent } from "./components/CallConnectedContent";
import { CallDeclinedContent } from "./components/CallDeclinedContent";
import { CallNoResponseContent } from "./components/CallNoResponseContent";

// Import services for error handling
import { CallService } from "./services/callService";

export const CallDesktop: React.FC = () => {
  // Get all state from custom hook
  const state = useCallState();
  
  // Get all actions from custom hook
  const actions = useCallActions(state);
  
  // Setup all event listeners
  useCallEvents(state);
  
  // Get team members data
  const { teamMembers, isLoading: isLoadingTeamMembers, error: teamMembersError } = useTeamMembers();
  
  const { showWarning } = useToastHelpers();

  // Auto-close window after 2 seconds when error occurs
  useEffect(() => {
    if (state.error) {
      console.log('📞 [CALL_DESKTOP] Error detected, will close window in 2 seconds:', state.error);
      const timeout = setTimeout(async () => {
        try {
          console.log('📞 [CALL_DESKTOP] Closing call window due to error:', state.error);
          await CallService.closeCallWindow();
        } catch (error) {
          console.error('❌ [CALL_DESKTOP] Failed to close call window on error:', error);
        }
      }, 2000);

      // Cleanup timeout on unmount or error change
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [state.error]);

  // Call duration counter effect
  useEffect(() => {
    let interval: number | null = null;
    
    if (state.callStatus === "connected") {
      interval = window.setInterval(() => {
        state.setCallDuration(prev => prev + 1);
      }, 1000);
    }
    
    return () => {
      if (interval) window.clearInterval(interval);
    };
  }, [state.callStatus, state.setCallDuration]);

  // Start call process when callData is available (only for outgoing calls)
  useEffect(() => {
    if (state.callData && !state.isLoading) {
      // Only start call process for outgoing calls (team-sidebar source)
      if (state.callData.source === 'team-sidebar') {
        // Get target employee ID from window or callData
        const targetEmployeeId = (window as any).targetEmployeeId || state.callData.callee.id;
        if (targetEmployeeId) {
          actions.startCallProcess(targetEmployeeId);
        }
      } else if (state.callData.source === 'incoming-call') {
        // For incoming calls, set the meetId from callId if available
        if (state.callData.callId) {
          state.setMeetId(parseInt(state.callData.callId));
        }
        console.log('📞 [CALL_DESKTOP] Incoming call detected, skipping startCallProcess');
      }
    }
  }, [state.callData, state.isLoading]);

  // Helper function to render content based on current state
  const renderContent = () => {
    if (state.error) {
      return (
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="text-red-400 text-sm mb-2">Error loading call</div>
            <div className="text-white text-xs">{state.error}</div>
          </div>
        </div>
      );
    }

    if (state.showDeclinedMessage) {
      return <CallDeclinedContent />;
    }

    if (state.showNoResponseMessage) {
      return <CallNoResponseContent />;
    }

    if (state.isLoading) {
      return (
        <CallLoadingContent 
          callData={state.callData}
          onCancel={actions.handleClose}
        />
      );
    }

    if (state.callStatus === "ringing") {
      return (
        <CallRingingContent
          callData={state.callData}
          onCancel={actions.handleClose}
          onAnswer={actions.handleAnswer}
        />
      );
    }

    if (state.callStatus === "connected") {
      return (
        <CallConnectedContent
          callData={state.callData}
          callDuration={state.callDuration}
          participants={state.participants}
          setParticipants={state.setParticipants}
          showAddUser={state.showAddUser}
          setShowAddUser={state.setShowAddUser}
          searchQuery={state.searchQuery}
          setSearchQuery={state.setSearchQuery}
          isMuted={state.isMuted}
          setIsMuted={state.setIsMuted}
          isSpeakerOn={state.isSpeakerOn}
          setIsSpeakerOn={state.setIsSpeakerOn}
          meetId={state.meetId}
          teamMembers={teamMembers}
          onClose={actions.handleClose}
        />
      );
    }

    // Default fallback
    return (
      <CallLoadingContent 
        callData={state.callData}
        onCancel={actions.handleClose}
      />
    );
  };

  return (
    <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex flex-col relative overflow-hidden">
      {/* Window Controls */}
      <div 
        className="h-8 bg-transparent flex items-center justify-end px-2 space-x-1 absolute top-0 right-0 z-50"
        data-tauri-drag-region
      >
        <button
          onClick={actions.handleMinimize}
          className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-400 transition-colors"
          title="Minimize"
        />
        <button
          onClick={actions.handleClose}
          className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-400 transition-colors"
          title="Close"
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col pt-8">
        {renderContent()}
      </div>

      {/* Toast Container */}
      <ToastContainer />
    </div>
  );
};