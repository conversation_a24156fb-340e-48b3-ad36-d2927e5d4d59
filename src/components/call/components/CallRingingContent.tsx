import React from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { TauriIcon as Icon } from "../../TauriIcon";
import { CustomAvatar } from "../../CustomAvatar";
import type { CallWindowData } from "../../../types/CallTypes";

interface CallRingingContentProps {
  callData: CallWindowData | null;
  onCancel: () => Promise<void>;
  onAnswer?: () => Promise<void>;
}

export const CallRingingContent: React.FC<CallRingingContentProps> = ({
  callData,
  onCancel,
  onAnswer
}) => {
  const isIncomingCall = callData?.source === 'incoming-call';
  return (
    <div className="p-8 h-full">
      <div className="flex flex-col items-center h-full">
        <div className="flex items-center mb-4">
          <h4 className="text-white text-sm font-medium">
            {isIncomingCall ? 'Incoming Call' : 'Calling'}
          </h4>
          <div className="flex ml-2 space-x-1">
            <div className="w-1 h-1 bg-[#1B84FF] rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
            <div className="w-1 h-1 bg-[#1B84FF] rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
            <div className="w-1 h-1 bg-[#1B84FF] rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
          </div>
        </div>

        <div className="flex justify-center flex-grow items-center mb-4 relative flex-col">
          {/* Ripple Animation Container */}
          <div className="relative mb-6">
            {/* Ripple Circles */}
            <div className="absolute inset-0 w-20 h-20 bg-[#1B84FF] opacity-20 rounded-full animate-ping" style={{animationDuration: '2s'}}></div>
            <div className="absolute inset-0 w-20 h-20 bg-[#1B84FF] opacity-30 rounded-full animate-ping" style={{animationDuration: '2s', animationDelay: '0.5s'}}></div>
            
            {/* Avatar Container */}
            <div className="relative z-10 flex justify-center items-center w-20 h-20">
              {isIncomingCall ? (
                // For incoming calls, show only the caller's avatar centered
                <CustomAvatar
                  src={callData?.caller?.avatar}
                  fallbackSrc="https://img.heroui.chat/image/avatar?w=200&h=200&u=1"
                  name={callData?.caller?.name || "Caller"}
                  size="lg"
                  className="ring-2 ring-[#1B84FF] ring-opacity-50"
                />
              ) : (
                // For outgoing calls, show both avatars
                <>
                  {/* Caller avatar */}
                  <CustomAvatar
                    src={callData?.caller?.avatar}
                    fallbackSrc="https://img.heroui.chat/image/avatar?w=200&h=200&u=1"
                    name={callData?.caller?.name || "Caller"}
                    size="md"
                    className="absolute -left-2 ring-2 ring-[#1B84FF] ring-opacity-50"
                  />

                  {/* Callee avatar with pulse */}
                  <CustomAvatar
                    src={callData?.callee?.avatar}
                    fallbackSrc="https://img.heroui.chat/image/avatar?w=200&h=200&u=2"
                    name={callData?.callee?.name || "User"}
                    size="md"
                    className="absolute left-12 ring-2 ring-green-400 ring-opacity-60 animate-pulse"
                  />
                </>
              )}
            </div>
          </div>
          
          <div className="text-[#C0C4CC] text-sm mb-2 animate-pulse">
            {isIncomingCall ? callData?.caller?.name : callData?.callee?.name}
          </div>
          <div className="text-[#7D8597] text-xs">
            {isIncomingCall ? callData?.caller?.role : callData?.callee?.role}
          </div>
        </div>

        {/* Action buttons */}
        {isIncomingCall ? (
          // For incoming calls, show Answer and Decline buttons
          <div className="flex gap-3 w-full">
            <Button
              color="danger"
              variant="flat"
              size="sm"
              className="flex-1 transition-all duration-300 hover:scale-105"
              startContent={<Icon icon="lucide:phone-off" className="text-sm" />}
              onPress={onCancel}
            >
              Decline
            </Button>
            <Button
              color="success"
              variant="flat"
              size="sm"
              className="flex-1 transition-all duration-300 hover:scale-105 bg-green-600 hover:bg-green-700 text-white"
              startContent={<Icon icon="lucide:phone" className="text-sm" />}
              onPress={onAnswer}
            >
              Answer
            </Button>
          </div>
        ) : (
          // For outgoing calls, show only Cancel button
          <Button
            color="danger"
            variant="flat"
            size="sm"
            className="w-full transition-all duration-300 hover:scale-105"
            startContent={<Icon icon="lucide:phone-off" className="text-sm" />}
            onPress={onCancel}
          >
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
};