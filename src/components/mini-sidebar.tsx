import React from "react";
import { TauriIcon as Icon } from "./TauriIcon";
import { Tooltip } from "@heroui/react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";

const parentApps = [
  { id: "time", label: "Time Tracking", icon: "lucide:clock" },
  { id: "meet", label: "Meet App", icon: "lucide:phone" }, // Added Meet app
  { id: "chat", label: "Chat App", icon: "lucide:message-circle" },
  { id: "project", label: "Project Management", icon: "lucide:kanban" },
  { id: "file", label: "File Manager", icon: "lucide:file" },
  { id: "requests", label: "Organizational Requests", icon: "lucide:file-text" },
  { id: "pulse", label: "PulseBoard", icon: "lucide:zap" },

];

interface MiniSidebarProps {
  activeApp: string;
  onChangeApp: (appId: string) => void;
  onHoverApp?: (appId: string | null) => void;
}

export const MiniSidebar: React.FC<MiniSidebarProps> = ({ activeApp, onChangeApp, onHoverApp }) => {
  const navigate = useNavigate();
  const appPath = (id:string)=>{
    switch(id){
      case "time": return "/time-tracking";
      case "meet": return "/meet";
      case "chat": return "/chat";
      case "project": return "/project";
      case "file": return "/file";
      case "requests": return "/requests";
      case "pulse": return "/pulse-board/dashboard";
      default: return "/";
    }
  };
  return (
    <aside className="w-[68px] bg-custom-sidebar flex flex-col h-screen border-r border-custom-border">
      {/* Logo area */}
      <div className="p-4 flex justify-center">
        <div className="w-10 h-10 bg-custom-primary rounded-md flex items-center justify-center">
          <Icon icon="lucide:layout-dashboard" className="text-white text-xl" />
        </div>
      </div>
      
      {/* App Navigation */}
      <div className="overflow-y-auto flex-grow chat-scrollbar flex flex-col items-center py-4">
        <div className="mt-6 flex flex-col items-center gap-4">
          {parentApps.map((app) => (
            <Tooltip 
              key={app.id} 
              content={app.label} 
              placement="right"
              className="w-full"
            >
              <button
                onClick={() => { onChangeApp(app.id); navigate(appPath(app.id)); }}
                onMouseEnter={() => onHoverApp && onHoverApp(app.id)}
                onMouseLeave={() => onHoverApp && onHoverApp(null)}
                className="relative w-12 h-12 flex items-center justify-center rounded-lg transition-all"
              >
                {activeApp === app.id && (
                  <motion.div
                    layoutId="active-app-indicator"
                    className="absolute inset-0 bg-primary/10 rounded-lg"
                    initial={false}
                    transition={{ type: "spring", duration: 0.5 }}
                  />
                )}
                {activeApp === app.id && (
                  <motion.div
                    layoutId="active-app-border"
                    className="absolute left-0 top-0 bottom-0 w-0.5 bg-primary rounded-full"
                    initial={false}
                    transition={{ type: "spring", duration: 0.5 }}
                  />
                )}
                <Icon 
                  icon={app.icon} 
                  className={`text-2xl z-10 ${
                    activeApp === app.id ? "text-primary" : "text-custom-text"
                  }`} 
                />
              </button>
            </Tooltip>
          ))}
        </div>
      </div>
      
      <div className="mt-auto p-4 flex justify-center">
        <Tooltip content="Settings" placement="right">
          <button className="w-10 h-10 rounded-lg flex items-center justify-center hover:bg-primary/10 text-custom-text">
            <Icon icon="lucide:settings" className="text-xl" />
          </button>
        </Tooltip>
      </div>
    </aside>
  );
};