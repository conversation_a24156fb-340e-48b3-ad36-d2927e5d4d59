import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Ava<PERSON>, Button, Badge, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Tooltip } from "@heroui/react";
import { getCurrentWindow } from '@tauri-apps/api/window';
import { invoke } from '@tauri-apps/api/core';
import { TauriIcon as Icon } from "./TauriIcon";
import { useTimeTracking } from "../context/TimeTrackingContext";
import { useUser } from "../context/UserContext";
import { ConnectionStatusDot } from "./ConnectionStatusBadge";


import { ScreenVisibility } from "../utils/visibility";

interface TopBarProps {
  showEmployeeList: boolean;
  toggleEmployeeList: () => void;
  activeApp: string;
  onMinimize?: () => void;
  onMaximize?: () => void;
  onClose?: () => void;
  onLogout?: () => void;
}

export const TopBar: React.FC<TopBarProps> = ({
  showEmployeeList,
  toggleEmployeeList,
  activeApp,
  onMinimize,
  onMaximize,
  onClose,
  onLogout
}) => {
  const { userStatus, setUserStatus, isTracking } = useTimeTracking();
  const { user, isLoading: userLoading, isFromCache, isOfflineMode, hasError, toggleScreenActive, incompleteActivity } = useUser();

  const [isMaximized, setIsMaximized] = useState(false);
  const [appWindow, setAppWindow] = useState<any>(null);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isScreenActiveLoading, setIsScreenActiveLoading] = useState(false);

  useEffect(() => {
    const initWindow = async () => {
      const window = getCurrentWindow();
      
      setAppWindow(window);

      // Check initial maximized state
      try {
        const maximized = await window.isMaximized();
        setIsMaximized(maximized);
      } catch (error) {
        console.error('Failed to get window maximized state:', error);
      }
    };

    initWindow();
  }, []);

  // Check if mandatory modal should be shown (incomplete activity exists)
  const isMandatoryModalOpen = incompleteActivity !== null;

  // Close dropdown when mandatory modal opens
  React.useEffect(() => {
    if (isMandatoryModalOpen) {
      setIsUserDropdownOpen(false);
    }
  }, [isMandatoryModalOpen]);

  // Debug user data
  React.useEffect(() => {
    console.log('🖼️ [TOPBAR] User data:', user);
    console.log('🖼️ [TOPBAR] User avatar:', user?.avatar);
    console.log('🖼️ [TOPBAR] User avatar type:', typeof user?.avatar);
    console.log('🖼️ [TOPBAR] User loading:', userLoading);
    console.log('🖼️ [TOPBAR] Has error:', hasError);
  }, [user, userLoading, hasError]);

  const handleMinimize = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.minimize();
        onMinimize?.();
      }
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  }, [appWindow, onMinimize]);

  const handleMaximize = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.toggleMaximize();
        const maximized = await appWindow.isMaximized();
        setIsMaximized(maximized);
        onMaximize?.();
      }
    } catch (error) {
      console.error('Failed to toggle maximize window:', error);
    }
  }, [appWindow, onMaximize]);

  const handleClose = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.close();
        onClose?.();
      }
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  }, [appWindow, onClose]);

  // Handle eye button click to toggle screen visibility
  const handleEyeButtonClick = useCallback(async () => {
    if (isScreenActiveLoading || !user) return;

    console.log('👁️ [TOPBAR] Eye button clicked, current state:', user.screen_active);

    try {
      // Set loading state
      setIsScreenActiveLoading(true);
      console.log('⏳ [TOPBAR] Setting screen active loading state...');

      // Call the toggle function
      await toggleScreenActive();

      console.log('✅ [TOPBAR] Screen active toggle completed successfully');
    } catch (error) {
      console.error('❌ [TOPBAR] Failed to toggle screen visibility:', error);
    } finally {
      // Always clear loading state
      setIsScreenActiveLoading(false);
      console.log('🔄 [TOPBAR] Screen active loading state cleared');
    }
  }, [toggleScreenActive, isScreenActiveLoading, user]);



  // Status badge color mapping - memoized for performance
  const statusColor = useMemo((): "success" | "warning" | "default" => {
    // If in offline mode, data is from cache, or has error, show as offline (gray)
    if (isOfflineMode || isFromCache || hasError) {
      return "default";
    }

    // If not tracking, force offline status color
    if (!isTracking) {
      return "default";
    }

    switch (userStatus) {
      case "available": return "success";
      case "meeting": return "warning";
      case "offline": return "default";
      default: return "success";
    }
  }, [userStatus, isFromCache, isOfflineMode, hasError, isTracking]);

  // Status label mapping - memoized for performance
  const statusLabel = useMemo((): string => {
    // If in offline mode, show offline status
    if (isOfflineMode) {
      return "Offline Mode";
    }

    // If data is from cache, show as offline until fresh data is loaded
    if (isFromCache) {
      return "Offline";
    }

    // If there's an error (API failed), show as offline
    if (hasError) {
      return "Offline";
    }

    // If not tracking, force offline status label
    if (!isTracking) {
      return "Offline";
    }

    switch (userStatus) {
      case "available": return "Online & Available";
      case "meeting": return "In a Meeting";
      case "offline": return "Offline";
      default: return "Online & Available";
    }
  }, [userStatus, isFromCache, isOfflineMode, hasError, isTracking]);

  // TopBar should always be full-width; do not shift based on active app
  const shiftClasses = "";

  // Memoize window control button props for modern top-right corner design
  const windowControlsConfig = useMemo(() => ({
    minimize: {
      icon: "lucide:minus",
      title: "Minimize",
      handler: handleMinimize
    },
    maximize: {
      icon: isMaximized ? "lucide:copy" : "lucide:square",
      title: isMaximized ? 'Restore' : 'Maximize',
      handler: handleMaximize
    },
    close: {
      icon: "lucide:x",
      title: "Close",
      handler: handleClose
    }
  }), [isMaximized, handleMinimize, handleMaximize, handleClose]);

  return (
    <header
      className={`
        h-[60px] border-b border-custom-border
        flex items-center px-6 relative z-[9999]
        bg-gradient-to-r from-custom-background via-custom-background to-custom-card/10
        backdrop-blur-md transition-all duration-300 ease-in-out
        shadow-sm
      `}
      data-tauri-drag-region
      style={{ userSelect: 'none' }}
    >
      {/* Modern Top-Right Controls - Inline Design */}
      <div
        className="absolute top-3 right-3 flex items-center gap-1 z-[10000]"
        style={{ pointerEvents: 'auto' }}
        data-tauri-drag-region="false"
      >
        {/* Action Buttons - Left Side */}
        <div className="flex items-center gap-2 mr-3" data-testid="action-buttons">
          {/* Eye Button - Dynamic Screen Visibility */}
          <Button
            isIconOnly
            variant="light"
            className={`
              w-8 h-8 min-w-8 text-custom-text/80 hover:text-white
              bg-custom-card/60 hover:bg-custom-card/80 active:bg-custom-card
              transition-all duration-200 ease-in-out
              hover:scale-105 active:scale-95
              focus:outline-none focus-visible:ring-2 focus-visible:ring-custom-primary/40
              rounded-md backdrop-blur-sm shadow-sm
              ${isMandatoryModalOpen || isScreenActiveLoading ? 'opacity-30 cursor-not-allowed' : ''}
              ${isScreenActiveLoading ? 'animate-pulse' : ''}
            `}
            onPress={isMandatoryModalOpen || isScreenActiveLoading ? undefined : handleEyeButtonClick}
            title={
              isMandatoryModalOpen
                ? "Disabled during modal"
                : isScreenActiveLoading
                  ? "Updating screen sharing status..."
                  : (user ? ScreenVisibility.getTooltip(user.screen_active) : "Screen visibility")
            }
            aria-label={
              isMandatoryModalOpen
                ? "Disabled during modal"
                : isScreenActiveLoading
                  ? "Updating screen sharing status..."
                  : (user ? ScreenVisibility.getAriaLabel(user.screen_active) : "Toggle screen visibility")
            }
            isDisabled={!user || userLoading || isMandatoryModalOpen || isScreenActiveLoading}
          >
            <Icon
              icon={user ? ScreenVisibility.getIcon(user.screen_active) : "lucide:eye"}
              className="text-lg"
            />
          </Button>

          {/* Employee List Toggle Button */}
          <Button
            isIconOnly
            variant="light"
            className={`
              w-8 h-8 min-w-8 transition-all duration-200 ease-in-out
              hover:scale-105 active:scale-95 rounded-md backdrop-blur-sm shadow-sm
              focus:outline-none focus-visible:ring-2 focus-visible:ring-opacity-40
              ${isMandatoryModalOpen ? 'opacity-30 cursor-not-allowed' : ''}
              ${showEmployeeList
                ? " hover:bg-primary/25 focus-visible:ring-custom-primary border border-custom-primary/20"
                : "text-custom-text/80 hover:text-white bg-custom-card/60 hover:bg-custom-card/80 active:bg-custom-card focus-visible:ring-custom-primary"
              }
            `}
            onPress={isMandatoryModalOpen ? undefined : toggleEmployeeList}
            title={isMandatoryModalOpen ? "Disabled during modal" : "Toggle employee list"}
            isDisabled={isMandatoryModalOpen}
          >
            <Icon icon="lucide:users" className="text-lg" />
          </Button>


        </div>

        {/* Separator */}
        <div className="w-px h-4 bg-custom-border/30 mx-1"></div>

        {/* Window Controls - Right Side */}
        <div className="flex items-center gap-1" data-testid="window-controls">
          {/* Minimize Button */}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            className="
              w-6 h-6 min-w-6 text-custom-text/70 hover:text-white
              hover:bg-yellow-500/20 active:bg-yellow-500/30
              transition-all duration-200 ease-in-out
              hover:scale-110 active:scale-95
              focus:outline-none focus:ring-1 focus:ring-yellow-500/30
              rounded-full backdrop-blur-sm
            "
            onPress={windowControlsConfig.minimize.handler}
            title={windowControlsConfig.minimize.title}
            data-testid="minimize-button"
          >
            <Icon icon={windowControlsConfig.minimize.icon} className="text-sm" />
          </Button>

          {/* Maximize/Restore Button */}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            className="
              w-6 h-6 min-w-6 text-custom-text/70 hover:text-white
              hover:bg-green-500/20 active:bg-green-500/30
              transition-all duration-200 ease-in-out
              hover:scale-110 active:scale-95
              focus:outline-none focus:ring-1 focus:ring-green-500/30
              rounded-full backdrop-blur-sm
            "
            onPress={windowControlsConfig.maximize.handler}
            title={windowControlsConfig.maximize.title}
            data-testid="maximize-button"
          >
            <Icon icon={windowControlsConfig.maximize.icon} className="text-sm" />
          </Button>

          {/* Close Button */}
          <Button
            isIconOnly
            variant="light"
            size="sm"
            className="
              w-6 h-6 min-w-6 text-custom-text/70 hover:text-white
              hover:bg-red-500/20 active:bg-red-500/30
              transition-all duration-200 ease-in-out
              hover:scale-110 active:scale-95
              focus:outline-none focus:ring-1 focus:ring-red-500/30
              rounded-full backdrop-blur-sm
            "
            onPress={windowControlsConfig.close.handler}
            title={windowControlsConfig.close.title}
            data-testid="close-button"
          >
            <Icon icon={windowControlsConfig.close.icon} className="text-sm" />
          </Button>
        </div>
      </div>

      {/* Left section - User info (non-draggable) */}
      <div
        className="flex items-center gap-3"
        style={{ pointerEvents: 'auto' }}
        data-tauri-drag-region="false"
      >
        <div className="flex items-center">
          <Dropdown
            isOpen={isUserDropdownOpen}
            onOpenChange={setIsUserDropdownOpen}
            isDisabled={isMandatoryModalOpen}
          >
            <DropdownTrigger>
              <div
                className={`flex items-center rounded-lg p-1 transition-all duration-200 ${
                  isMandatoryModalOpen
                    ? 'opacity-30 cursor-not-allowed'
                    : 'cursor-pointer hover:bg-custom-card/30'
                }`}
                onClick={() => {
                  if (!isMandatoryModalOpen) {
                    setIsUserDropdownOpen(!isUserDropdownOpen);
                  }
                }}
              >
                <Badge
                  content=""
                  color={statusColor}
                  shape="circle"
                  placement="bottom-right"
                  className="ring-2 ring-custom-background"
                >
                  <Avatar
                    src={user?.avatar || "https://img.heroui.chat/image/avatar?w=200&h=200&u=5"}
                    className="h-9 w-9 ring-1 ring-custom-border/20"
                    showFallback
                    name={user?.full_name || "User"}
                    onError={() => {
                      console.error('🖼️ [TOPBAR] Avatar failed to load:', user?.avatar);
                    }}
                  />
                </Badge>
                <div className="ml-2">
                  <h3 className="text-white text-sm font-medium leading-tight">
                    {user?.full_name || "User"}
                  </h3>
                  <div className="flex items-center text-custom-muted text-xs leading-tight">
                    <span>{statusLabel}</span>
                    <Icon icon="lucide:chevron-down" className="ml-1 text-[10px] opacity-60" />
                  </div>
                </div>
              </div>
            </DropdownTrigger>
            <DropdownMenu
              aria-label="User status options"
              className="text-custom-text"
              variant="flat"
              disallowEmptySelection
              selectionMode="single"
              selectedKeys={!isTracking ? ["offline"] : [userStatus]}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0]?.toString() as "available" | "meeting" | "offline";
                if (selected) {
                  // Don't allow changing status if not tracking (should stay offline)
                  if (!isTracking) return;
                  // Don't allow setting to offline if tracking is active
                  if (isTracking && selected === "offline") return;
                  setUserStatus(selected);
                }
              }}
            >
              <DropdownItem
                key="available"
                startContent={<div className="w-2 h-2 rounded-full bg-success"></div>}
                description={!isTracking ? "Start tracking to go online" : "Ready to respond to messages"}
                className={!isTracking ? "opacity-50" : ""}
              >
                Online & Available {!isTracking && "(Requires tracking)"}
              </DropdownItem>
              <DropdownItem
                key="meeting"
                startContent={<div className="w-2 h-2 rounded-full bg-warning"></div>}
                description={!isTracking ? "Start tracking to set status" : "Currently in a meeting"}
                className={!isTracking ? "opacity-50" : ""}
              >
                In a Meeting {!isTracking && "(Requires tracking)"}
              </DropdownItem>
              <DropdownItem
                key="offline"
                startContent={<div className="w-2 h-2 rounded-full bg-default-400"></div>}
                description={!isTracking ? "Current status when not tracking" : "Currently unavailable"}
                className={!isTracking ? "bg-default-100/20" : ""}
              >
                Offline {!isTracking ? "(Current)" : isTracking ? "(Unavailable while tracking)" : ""}
              </DropdownItem>

              {/* Logout Option */}
              <DropdownItem
                key="logout"
                startContent={<Icon icon="lucide:log-out" className="text-red-500" />}
                description="Sign out of your account"
                className="text-red-500 data-[hover=true]:text-red-400"
                onPress={onLogout}
              >
                Logout
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>

      {/* Center Drag Region - Full width draggable area */}
      <div
        className="flex-1 h-full cursor-move pr-32 flex items-center justify-center"
        data-tauri-drag-region
        data-testid="center-drag-region"
        aria-label="Drag to move window"
        style={{ userSelect: 'none' }}
      >
        {/* App title - Modern glass-morphism effect without border */}
        <div className="flex items-center gap-3">
          <div className="
            text-custom-text/60 text-sm font-semibold tracking-wider
            backdrop-blur-md px-4 py-1.5 rounded-full
            bg-gradient-to-r from-custom-card/5 via-custom-card/10 to-custom-card/5
            shadow-inner transition-all duration-500 ease-out
            hover:text-custom-text/80 hover:backdrop-blur-lg
            hover:bg-gradient-to-r hover:from-custom-card/10 hover:via-custom-card/15 hover:to-custom-card/10
            hover:shadow-lg hover:scale-105
            relative overflow-hidden
            before:absolute before:inset-0 before:bg-gradient-to-r
            before:from-transparent before:via-white/5 before:to-transparent
            before:opacity-0 before:transition-opacity before:duration-300
            hover:before:opacity-100
            flex items-center space-x-2
          ">
            <span className="relative z-10 drop-shadow-sm">TeamBy Desktop</span>
            <ConnectionStatusDot className="relative z-10" />
          </div>


        </div>
      </div>
    </header>
  );
};