import { useEffect, useRef, useCallback, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface UseAutoHideScrollbarOptions {
  hideDelay?: number; // milliseconds
  showOnHover?: boolean;
}

export const useAutoHideScrollbar = <T extends HTMLElement>(
  options: UseAutoHideScrollbarOptions = {}
) => {
  const { hideDelay = 2000, showOnHover = true } = options;
  const scrollRef = useRef<T>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isScrollingRef = useRef(false);
  const [isLinux, setIsLinux] = useState<boolean>(false);

  // Detect platform
  useEffect(() => {
    const detectPlatform = async () => {
      try {
        const platform = await invoke<string>('get_platform');
        setIsLinux(platform === 'linux');
      } catch (error) {
        // Fallback to user agent detection
        const isLinuxUA = navigator.platform.toLowerCase().includes('linux') || 
                          navigator.userAgent.toLowerCase().includes('linux');
        setIsLinux(isLinuxUA);
      }
    };
    detectPlatform();
  }, []);

  const showScrollbar = useCallback(() => {
    if (scrollRef.current && !isLinux) {
      scrollRef.current.classList.add('is-scrolling');
      isScrollingRef.current = true;
    }
  }, [isLinux]);

  const hideScrollbar = useCallback(() => {
    if (scrollRef.current && !isLinux && isScrollingRef.current) {
      scrollRef.current.classList.remove('is-scrolling');
      isScrollingRef.current = false;
    }
  }, [isLinux]);

  const handleScroll = useCallback(() => {
    if (!isLinux) {
      showScrollbar();
      
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // Set new timeout to hide scrollbar
      timeoutRef.current = setTimeout(() => {
        hideScrollbar();
      }, hideDelay);
    }
  }, [showScrollbar, hideScrollbar, hideDelay, isLinux]);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    // Apply appropriate CSS class based on platform
    if (isLinux) {
      element.classList.add('linux-hide-scrollbar');
    } else {
      element.classList.add('auto-hide-scrollbar');
    }

    // Add scroll listener (only needed for non-Linux)
    if (!isLinux) {
      element.addEventListener('scroll', handleScroll);

      // Handle mouse enter/leave if showOnHover is enabled
      const handleMouseEnter = () => {
        if (showOnHover && !isScrollingRef.current) {
          showScrollbar();
        }
      };

      const handleMouseLeave = () => {
        if (showOnHover && !isScrollingRef.current) {
          hideScrollbar();
        }
      };

      if (showOnHover) {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
      }

      // Cleanup for non-Linux
      return () => {
        element.removeEventListener('scroll', handleScroll);
        if (showOnHover) {
          element.removeEventListener('mouseenter', handleMouseEnter);
          element.removeEventListener('mouseleave', handleMouseLeave);
        }
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }

    // Linux cleanup (minimal)
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [handleScroll, showOnHover, showScrollbar, hideScrollbar, isLinux]);

  return scrollRef;
};