import { useEffect, useRef, useState } from 'react';

export const useScrollShadow = <T extends HTMLElement>() => {
  const scrollRef = useRef<T>(null);
  const [hasScrollTop, setHasScrollTop] = useState(false);
  const [hasScrollBottom, setHasScrollBottom] = useState(false);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    const checkScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = element;
      setHasScrollTop(scrollTop > 10);
      setHasScrollBottom(scrollTop + clientHeight < scrollHeight - 10);
    };

    // Initial check
    checkScroll();

    // Add scroll listener
    element.addEventListener('scroll', checkScroll);
    
    // Add resize observer to detect content changes
    const resizeObserver = new ResizeObserver(checkScroll);
    resizeObserver.observe(element);

    return () => {
      element.removeEventListener('scroll', checkScroll);
      resizeObserver.disconnect();
    };
  }, []);

  const scrollShadowClasses = `scroll-shadow ${hasScrollTop ? 'has-scroll-top' : ''} ${hasScrollBottom ? 'has-scroll-bottom' : ''}`;

  return { scrollRef, scrollShadowClasses };
};