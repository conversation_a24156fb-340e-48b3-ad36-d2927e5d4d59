/* Google Fonts removed for Tauri compatibility */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Shimmer Animation for Skeleton Loaders */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Call Ringing Animations */
@keyframes callPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(27, 132, 255, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(27, 132, 255, 0);
  }
}

@keyframes callBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes callRipple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

.animate-call-pulse {
  animation: callPulse 2s ease-in-out infinite;
}

.animate-call-bounce {
  animation: callBounce 1s ease-in-out infinite;
}

.animate-call-ripple {
  animation: callRipple 1.5s ease-out infinite;
}

/* Custom colors for dark theme time tracking app */
:root {
  --custom-background: #0F111A;
  --custom-card: #1A1D2B;
  --custom-sidebar: #12141F;
  --custom-border: #2A2D3C;
  --custom-text: #FFFFFF;
  --custom-muted: #7D8597;
  --custom-body: #C0C4CC;
  --custom-primary: #1B84FF;
}

body {
  @apply bg-custom-background text-custom-body;
  /* Prevent text selection on UI elements */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for specific elements that should be selectable */
.selectable-text,
input,
textarea,
[contenteditable="true"],
.prose,
.content-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Prevent selection on interactive elements */
button,
.button,
[role="button"],
.cursor-pointer,
.clickable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Utility classes for text selection control */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.select-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.select-all {
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}

.select-auto {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}







.bg-custom-background { background-color: var(--custom-background); }
.bg-custom-card { background-color: var(--custom-card); }
.bg-custom-sidebar { background-color: var(--custom-sidebar); }
.bg-custom-primary { background-color: var(--custom-primary); }
.border-custom-border { border-color: var(--custom-border); }
.border-custom-primary { border-color: var(--custom-primary); }
.text-custom-text { color: var(--custom-text); }
.text-custom-muted { color: var(--custom-muted); }
.text-custom-body { color: var(--custom-body); }
.text-custom-primary { color: var(--custom-primary); }

.active-link {
  @apply bg-primary/10 text-primary border-l-2 border-primary;
}

.mini-sidebar-item {
  @apply relative w-12 h-12 flex items-center justify-center rounded-lg cursor-pointer transition-colors;
}

.mini-sidebar-item.active {
  @apply bg-primary/10;
}

.mini-sidebar-item.active::before {
  content: "";
  @apply absolute left-0 top-0 bottom-0 w-0.5 bg-primary rounded-full;
}

.timer-display {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.5px;
}

/* Modern Telegram-inspired Scrollbar - Hidden by default, visible on hover */
.custom-scrollbar,
.chat-scrollbar {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent;
  scroll-behavior: smooth;
}

.custom-scrollbar:hover,
.chat-scrollbar:hover {
  scrollbar-color: rgba(128, 128, 128, 0.3) transparent;
}

/* WebKit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar,
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track,
.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb,
.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb,
.chat-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: rgba(128, 128, 128, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover,
.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(128, 128, 128, 0.5);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active,
.chat-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: rgba(128, 128, 128, 0.7);
}

/* Apply modern scrollbar to all scrollable elements */
.overflow-y-auto,
.overflow-auto,
.overflow-y-scroll,
.overflow-scroll,
main {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.overflow-y-auto:hover,
.overflow-auto:hover,
.overflow-y-scroll:hover,
.overflow-scroll:hover,
main:hover {
  scrollbar-color: rgba(128, 128, 128, 0.3) transparent;
}

/* WebKit browsers */
.overflow-y-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
.overflow-y-scroll::-webkit-scrollbar,
.overflow-scroll::-webkit-scrollbar,
main::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track,
.overflow-auto::-webkit-scrollbar-track,
.overflow-y-scroll::-webkit-scrollbar-track,
.overflow-scroll::-webkit-scrollbar-track,
main::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb,
.overflow-auto::-webkit-scrollbar-thumb,
.overflow-y-scroll::-webkit-scrollbar-thumb,
.overflow-scroll::-webkit-scrollbar-thumb,
main::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.overflow-y-auto:hover::-webkit-scrollbar-thumb,
.overflow-auto:hover::-webkit-scrollbar-thumb,
.overflow-y-scroll:hover::-webkit-scrollbar-thumb,
.overflow-scroll:hover::-webkit-scrollbar-thumb,
main:hover::-webkit-scrollbar-thumb {
  background-color: rgba(128, 128, 128, 0.3);
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.overflow-auto::-webkit-scrollbar-thumb:hover,
.overflow-y-scroll::-webkit-scrollbar-thumb:hover,
.overflow-scroll::-webkit-scrollbar-thumb:hover,
main::-webkit-scrollbar-thumb:hover {
  background-color: rgba(128, 128, 128, 0.5);
}

.overflow-y-auto::-webkit-scrollbar-thumb:active,
.overflow-auto::-webkit-scrollbar-thumb:active,
.overflow-y-scroll::-webkit-scrollbar-thumb:active,
.overflow-scroll::-webkit-scrollbar-thumb:active,
main::-webkit-scrollbar-thumb:active {
  background-color: rgba(128, 128, 128, 0.7);
}

/* Linux-specific: Hide scrollbars completely due to WebKitGTK limitations */
.linux-hide-scrollbar {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  overflow-y: auto !important; /* Still allow scrolling */
}

.linux-hide-scrollbar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Windows/Other OS: Custom scrollbar with auto-hide */
.auto-hide-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.auto-hide-scrollbar:hover,
.auto-hide-scrollbar.is-scrolling {
  scrollbar-color: rgba(128, 128, 128, 0.3) transparent;
}

.auto-hide-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.auto-hide-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.auto-hide-scrollbar:hover::-webkit-scrollbar-thumb,
.auto-hide-scrollbar.is-scrolling::-webkit-scrollbar-thumb {
  background-color: rgba(128, 128, 128, 0.3);
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb:hover,
.auto-hide-scrollbar.is-scrolling::-webkit-scrollbar-thumb:hover {
  background-color: rgba(128, 128, 128, 0.5);
}

.auto-hide-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: rgba(128, 128, 128, 0.7);
}

/* Mobile - completely hide scrollbar */
@media (max-width: 768px) {
  .custom-scrollbar,
  .chat-scrollbar,
  .auto-hide-scrollbar,
  .overflow-y-auto,
  .overflow-auto,
  .overflow-y-scroll,
  .overflow-scroll,
  main {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  
  .custom-scrollbar::-webkit-scrollbar,
  .chat-scrollbar::-webkit-scrollbar,
  .auto-hide-scrollbar::-webkit-scrollbar,
  .overflow-y-auto::-webkit-scrollbar,
  .overflow-auto::-webkit-scrollbar,
  .overflow-y-scroll::-webkit-scrollbar,
  .overflow-scroll::-webkit-scrollbar,
  main::-webkit-scrollbar {
    display: none !important;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .custom-scrollbar:hover,
  .chat-scrollbar:hover,
  .overflow-y-auto:hover,
  .overflow-auto:hover,
  .overflow-y-scroll:hover,
  .overflow-scroll:hover,
  main:hover {
    scrollbar-color: rgba(160, 160, 160, 0.3) transparent;
  }
  
  .custom-scrollbar:hover::-webkit-scrollbar-thumb,
  .chat-scrollbar:hover::-webkit-scrollbar-thumb,
  .overflow-y-auto:hover::-webkit-scrollbar-thumb,
  .overflow-auto:hover::-webkit-scrollbar-thumb,
  .overflow-y-scroll:hover::-webkit-scrollbar-thumb,
  .overflow-scroll:hover::-webkit-scrollbar-thumb,
  main:hover::-webkit-scrollbar-thumb {
    background-color: rgba(160, 160, 160, 0.3);
  }
  
  .custom-scrollbar:hover::-webkit-scrollbar-thumb:hover,
  .chat-scrollbar:hover::-webkit-scrollbar-thumb:hover,
  .overflow-y-auto:hover::-webkit-scrollbar-thumb:hover,
  .overflow-auto:hover::-webkit-scrollbar-thumb:hover,
  .overflow-y-scroll:hover::-webkit-scrollbar-thumb:hover,
  .overflow-scroll:hover::-webkit-scrollbar-thumb:hover,
  main:hover::-webkit-scrollbar-thumb:hover {
    background-color: rgba(160, 160, 160, 0.5);
  }
}

/* Ensure smooth scrolling everywhere */
* {
  scroll-behavior: smooth;
}

/* Add subtle shadow when scrollable content overflows */
.scroll-shadow {
  position: relative;
}

.scroll-shadow::before,
.scroll-shadow::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.scroll-shadow::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(26, 29, 43, 0.8), transparent);
}

.scroll-shadow::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(26, 29, 43, 0.8), transparent);
}

.scroll-shadow.has-scroll-top::before {
  opacity: 1;
}

.scroll-shadow.has-scroll-bottom::after {
  opacity: 1;
}

/* Add responsive chat layout styles */
.chat-main-content {
  transition: all 0.3s ease;
}

/* On medium and larger screens leave space for mini-sidebar (68px) + expanded sidebar (320px) */
/* Removed margin-left to fix TeamSidebar width issue in different sections */

.chat-main-content .max-w-7xl {
  width: 100%;
  max-width: none;
}

/* Make chat view responsive when employee list is closed */
@media (min-width: 768px) {
  .chat-main-content .grid-cols-12 > .md\:col-span-7 {
    transition: all 0.3s ease;
  }
  
  .chat-main-content .grid-cols-12 > .md\:col-span-5 {
    transition: all 0.3s ease;
  }
}

/* Animation for cards */
@keyframes card-hover {
  0% { transform: translateY(0); }
  100% { transform: translateY(-3px); }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 768px) {
  .kanban-mobile-view {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-padding: 1rem;
  }
  
  .kanban-mobile-view > div {
    scroll-snap-align: start;
    flex: 0 0 85%;
  }
}

/* Add styles to make drag and drop work better */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #2A2D3C transparent;
}

/* Add these styles to improve drag and drop */
.is-dragging {
  cursor: grabbing !important;
  pointer-events: auto !important;
  position: relative;
  z-index: 9999;
}

/* This ensures dragged items remain visible */
[data-rbd-drag-handle-context-id] {
  cursor: grab;
}

[data-rbd-drag-handle-context-id]:active {
  cursor: grabbing;
}

/* Make sure the droppable area is easily visible */
[data-rbd-droppable-id] {
  min-height: 50px; /* Ensure droppable areas have sufficient height */
}