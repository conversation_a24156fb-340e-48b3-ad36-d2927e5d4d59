import React from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { Hero<PERSON><PERSON>rovider } from "@heroui/react"
import { <PERSON>h<PERSON>out<PERSON> } from "react-router-dom";
import App from './App.tsx'
import './index.css'
import { initResizeObserverErrorHandler } from './utils/resizeObserverErrorHandler'

// Initialize ResizeObserver error handling before rendering
initResizeObserverErrorHandler();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <HeroUIProvider>
      <HashRouter>
        <App />
      </HashRouter>
    </HeroUIProvider>
  </React.StrictMode>,
)
