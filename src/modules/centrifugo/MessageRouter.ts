// Centrifugo Message Router
// Routes messages to appropriate services based on message type

import { listen } from '@tauri-apps/api/event';
import { invoke } from '@tauri-apps/api/core';
import { employeeStatusService, type CentrifugoEmployeeMessage } from './EmployeeStatusService';
import { liveKitScreenShareService, type LiveKitScreenShareMessage } from './LiveKitScreenShareService';

export class CentrifugoMessageRouter {
  private isListening = false;
  private unlisten: (() => void) | null = null;

  // Start listening for messages from Rust backend
  async startListening(): Promise<void> {
    if (this.isListening) {
      console.log('📨 [CENTRIFUGO-ROUTER] Already listening for messages');
      return;
    }

    try {
      console.log('📨 [CENTRIFUGO-ROUTER] Starting message listener...');
      
      this.unlisten = await listen<any>('centrifugo-message', (event) => {
        console.log('📨 *****[CENTRIFUGO-ROUTER]***** Received message from Rust:', event.payload);
        // Debug log to Tauri terminal
        invoke('debug_log', {
          message: `Received message from Rust: ${JSON.stringify(event.payload)}`,
          tag: 'CENTRIFUGO-ROUTER'
        });
        this.routeMessage(event.payload);
      });

      this.isListening = true;
      console.log('✅ [CENTRIFUGO-ROUTER] Message listener started');
    } catch (error) {
      console.error('❌ [CENTRIFUGO-ROUTER] Failed to start listener:', error);
    }
  }

  // Stop listening for messages
  async stopListening(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    if (this.unlisten) {
      this.unlisten();
      this.unlisten = null;
    }

    this.isListening = false;
    console.log('🛑 [CENTRIFUGO-ROUTER] Message listener stopped');
  }

  // Route message to appropriate service based on type
  private routeMessage(data: any): void {
    try {
      const messageType = data.type;
      console.log(`📨 [CENTRIFUGO-ROUTER] Routing message type: ${messageType}`);

      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Routing message type: ${messageType}`,
        tag: 'CENTRIFUGO-ROUTER'
      });

      switch (messageType) {
        case 'employee_online':
        case 'employee_offline':
          // Route to Employee Status Service
          const employeeMessage: CentrifugoEmployeeMessage = {
            employee_id: data.employee_id,
            status: data.status,
            timestamp: data.timestamp,
            type: messageType
          };

          console.log('👥 [CENTRIFUGO-ROUTER] Routing to Employee Status Service');
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `Routing to Employee Status Service for employee_id: ${data.employee_id}`,
            tag: 'CENTRIFUGO-ROUTER'
          });

          employeeStatusService.processMessage(employeeMessage);
          break;

        case 'employee_update_screen_view':
          // Route to UserContext via custom event
          console.log('👁️ [CENTRIFUGO-ROUTER] Routing screen view update to UserContext');
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `Routing screen view update for employee_id: ${data.employee_id}, screen_active: ${data.screen_active}`,
            tag: 'CENTRIFUGO-ROUTER'
          });

          // Create custom event for UserContext
          const screenViewEvent = new CustomEvent('centrifugo-employee-status', {
            detail: {
              message_type: messageType,
              timestamp: data.timestamp,
              employee_id: data.employee_id,
              employee_name: data.employee_name,
              status: data.status,
              screen_active: data.screen_active
            }
          });

          // Dispatch event to window
          window.dispatchEvent(screenViewEvent);
          break;

        case 'livekit_share_ascreen_view':
          // Check platform and route accordingly
          console.log('📺 [CENTRIFUGO-ROUTER] Processing LiveKit screen share message');
          console.log('🚨 [CENTRIFUGO-ROUTER] NEW CODE VERSION - SHOULD OPEN EXTERNAL BROWSER!');

          // Get platform information and handle routing
          (async () => {
            try {
              const platformInfo = await invoke<string>('get_platform');
              console.log('🖥️ [CENTRIFUGO-ROUTER] Detected platform:', platformInfo);

              if (platformInfo === 'linux') {
                // Linux: Open in external browser
                console.log('🐧 [CENTRIFUGO-ROUTER] Linux detected - opening LiveKit in external browser');

                try {
                  // Get current user profile for browser URL
                  const currentUserProfile = await invoke<any>('get_cached_user_profile');

                  if (!currentUserProfile || !currentUserProfile.full_name) {
                    console.error('❌ [CENTRIFUGO-ROUTER] Current user profile not found');
                    return;
                  }

                  // Build browser URL with parameters for external browser
                  const params = new URLSearchParams({
                    employeeId: data.employee_id.toString(),
                    employeeName: encodeURIComponent(data.employee_name),
                    currentUserName: encodeURIComponent(currentUserProfile.full_name)
                  });

                  const browserUrl = `http://127.0.0.1:3000/#/livekit-browser?${params.toString()}`;

                  console.log('🔗 [CENTRIFUGO-ROUTER] Opening external browser URL:', browserUrl);

                  // Open in external browser using Tauri command
                  console.log('🌐 [CENTRIFUGO-ROUTER] About to call open_in_browser...');
                  await invoke('open_in_browser', { url: browserUrl });
                  console.log('🌐 [CENTRIFUGO-ROUTER] open_in_browser call completed');

                  console.log('✅ [CENTRIFUGO-ROUTER] LiveKit external browser opened successfully');

                  // Debug log to Tauri terminal
                  invoke('debug_log', {
                    message: `Opened LiveKit in external browser for employee_id: ${data.employee_id}, employee_name: ${data.employee_name}`,
                    tag: 'CENTRIFUGO-ROUTER'
                  });

                  // Return early to prevent fallback execution
                  return;

                } catch (error) {
                  console.error('❌ [CENTRIFUGO-ROUTER] Failed to open LiveKit in external browser:', error);
                  invoke('debug_log', {
                    message: `Failed to open LiveKit in external browser: ${error}`,
                    tag: 'CENTRIFUGO-ROUTER'
                  });
                }

              } else {
                // Non-Linux: Use existing LiveKit Screen Share Service
                const liveKitMessage: LiveKitScreenShareMessage = {
                  employee_id: data.employee_id,
                  employee_name: data.employee_name,
                  share_type: data.share_type,
                  timestamp: data.timestamp,
                  type: messageType
                };

                console.log('📺 [CENTRIFUGO-ROUTER] Routing to LiveKit Screen Share Service');
                // Debug log to Tauri terminal
                invoke('debug_log', {
                  message: `Routing to LiveKit Screen Share Service for employee_id: ${data.employee_id}, employee_name: ${data.employee_name}`,
                  tag: 'CENTRIFUGO-ROUTER'
                });

                liveKitScreenShareService.processMessage(liveKitMessage);
              }

            } catch (error) {
              console.error('❌ [CENTRIFUGO-ROUTER] Failed to get platform info:', error);
              // Fallback to existing service
              const liveKitMessage: LiveKitScreenShareMessage = {
                employee_id: data.employee_id,
                employee_name: data.employee_name,
                share_type: data.share_type,
                timestamp: data.timestamp,
                type: messageType
              };
              liveKitScreenShareService.processMessage(liveKitMessage);
            }
          })();
          break;

        case 'call_accepted':
          // Route call_accepted messages to call window
          console.log('📞 [CENTRIFUGO-ROUTER] Routing call_accepted message');
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `Routing call_accepted message from employee_id: ${data.from_employee_id}, meet_id: ${data.meet_id}`,
            tag: 'CENTRIFUGO-ROUTER'
          });
          
          // Forward to call window via custom event (as it's already emitted by Centrifugo service)
          // The message is already forwarded to all listeners via the original centrifugo-message event
          console.log('✅ [CENTRIFUGO-ROUTER] call_accepted message processed - already forwarded by Centrifugo service');
          break;

        case 'call_declined':
          // Route call_declined messages to call window
          console.log('📞 [CENTRIFUGO-ROUTER] ✅ Successfully routing call_declined message');
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `✅ Routing call_declined message from employee_id: ${data.from_employee_id}, meet_id: ${data.meet_id}`,
            tag: 'CENTRIFUGO-ROUTER'
          });

          // Forward to call window via custom event (as it's already emitted by Centrifugo service)
          // The message is already forwarded to all listeners via the original centrifugo-message event
          console.log('✅ [CENTRIFUGO-ROUTER] call_declined message processed - already forwarded by Centrifugo service');
          console.log('📞 [CENTRIFUGO-ROUTER] call_declined case handled successfully');
          break;

        case 'call_incoming':
          // Route incoming call messages to open call window
          console.log('📞 [CENTRIFUGO-ROUTER] Processing incoming call message');
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `Processing incoming call from employee_id: ${data.from_employee_id}, meet_id: ${data.meet_id}`,
            tag: 'CENTRIFUGO-ROUTER'
          });

          // Handle incoming call asynchronously
          (async () => {
            try {
              await this.handleIncomingCall(data);
            } catch (error) {
              console.error('❌ [CENTRIFUGO-ROUTER] Failed to handle incoming call:', error);
              invoke('debug_log', {
                message: `Failed to handle incoming call: ${error}`,
                tag: 'CENTRIFUGO-ROUTER'
              });
            }
          })();
          break;

        default:
          console.log(`⚠️ [CENTRIFUGO-ROUTER] No handler for message type: ${messageType}`);
          // Debug log to Tauri terminal
          invoke('debug_log', {
            message: `No handler for message type: ${messageType}`,
            tag: 'CENTRIFUGO-ROUTER'
          });
          break;
      }
    } catch (error) {
      console.error('❌ [CENTRIFUGO-ROUTER] Failed to route message:', error);
      // Debug log to Tauri terminal
      invoke('debug_log', {
        message: `Failed to route message: ${error}`,
        tag: 'CENTRIFUGO-ROUTER'
      });
    }
  }

  // Handle incoming call messages
  private async handleIncomingCall(data: any): Promise<void> {
    try {
      console.log('📞 [CENTRIFUGO-ROUTER] Handling incoming call:', data);

      // Get current user profile to create callee data
      const currentUser = await invoke<any>('get_cached_user_profile');
      if (!currentUser) {
        console.error('❌ [CENTRIFUGO-ROUTER] No cached user profile found for incoming call');
        return;
      }

      // Transform incoming call data to CallWindowData format
      const callWindowData = {
        caller: {
          id: data.from_employee_id,
          name: data.from_employee_name,
          fullName: data.from_employee_name,
          role: 'Employee', // Default role since not provided in message
          avatar: data.from_employee_avatar || '',
          isOnline: true
        },
        callee: {
          id: currentUser.id,
          name: currentUser.full_name,
          fullName: currentUser.full_name,
          role: currentUser.position || 'Employee',
          avatar: currentUser.avatar || '',
          isOnline: true
        },
        timestamp: data.timestamp || Date.now(),
        source: 'incoming-call' as const,
        callId: data.meet_id?.toString()
      };

      console.log('📞 [CENTRIFUGO-ROUTER] Transformed call data:', callWindowData);

      // Delegate to backend atomic handler to decide opening window vs showing notification
      const opened = await invoke<boolean>('handle_incoming_call', {
        message: data,
        callData: JSON.stringify(callWindowData)
      });
      if (opened) {
        console.log('✅ [CENTRIFUGO-ROUTER] Incoming call window opened by backend');
      } else {
        console.log('🔔 [CENTRIFUGO-ROUTER] Backend reported existing call window, notification requested');
      }

    } catch (error) {
      console.error('❌ [CENTRIFUGO-ROUTER] Failed to handle incoming call:', error);
      throw error;
    }
  }

  // Get current listening status
  isCurrentlyListening(): boolean {
    return this.isListening;
  }
}

// Singleton instance - Updated for call_declined support
export const centrifugoMessageRouter = new CentrifugoMessageRouter();
