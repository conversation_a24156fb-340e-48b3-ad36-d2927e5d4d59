import React from 'react';
import { Button } from '@heroui/react';
import { centrifugoMessageRouter } from './modules/centrifugo/MessageRouter';

export const IncomingCallTest: React.FC = () => {
  
  const simulateIncomingCall = () => {
    console.log('🧪 [TEST] Simulating incoming call message...');
    
    // Sample incoming call message data
    const incomingCallMessage = {
      action: "new_call",
      from_employee_avatar: "http://localhost:8000/media/user_avatars/logo2.png", 
      from_employee_id: 71,
      from_employee_name: "amir reza",
      meet_id: 100,
      meet_type: "audio", 
      timestamp: Date.now(),
      type: "call_incoming"
    };
    
    console.log('🧪 [TEST] Sending message to router:', incomingCallMessage);
    
    // Directly call the router's routeMessage method
    try {
      (centrifugoMessageRouter as any).routeMessage(incomingCallMessage);
      console.log('✅ [TEST] Message sent to router successfully');
    } catch (error) {
      console.error('❌ [TEST] Failed to send message to router:', error);
    }
  };

  const simulateCallAccepted = () => {
    console.log('🧪 [TEST] Simulating call accepted message...');
    
    const callAcceptedMessage = {
      from_employee_id: 71,
      meet_id: 100,
      type: "call_accepted",
      timestamp: Date.now()
    };
    
    console.log('🧪 [TEST] Sending call accepted message:', callAcceptedMessage);
    
    try {
      (centrifugoMessageRouter as any).routeMessage(callAcceptedMessage);
      console.log('✅ [TEST] Call accepted message sent successfully');
    } catch (error) {
      console.error('❌ [TEST] Failed to send call accepted message:', error);
    }
  };

  const simulateCallDeclined = () => {
    console.log('🧪 [TEST] Simulating call declined message...');
    
    const callDeclinedMessage = {
      from_employee_id: 71,
      meet_id: 100,
      type: "call_declined",
      timestamp: Date.now()
    };
    
    console.log('🧪 [TEST] Sending call declined message:', callDeclinedMessage);
    
    try {
      (centrifugoMessageRouter as any).routeMessage(callDeclinedMessage);
      console.log('✅ [TEST] Call declined message sent successfully');
    } catch (error) {
      console.error('❌ [TEST] Failed to send call declined message:', error);
    }
  };

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Incoming Call Test</h1>
      
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Test Controls</h2>
        <p className="text-gray-400 mb-4">
          Use these buttons to simulate incoming call messages and test the call window functionality.
        </p>
        
        <div className="flex gap-3 flex-wrap">
          <Button
            onClick={simulateIncomingCall}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Simulate Incoming Call
          </Button>
          
          <Button
            onClick={simulateCallAccepted}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Simulate Call Accepted
          </Button>
          
          <Button
            onClick={simulateCallDeclined}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            Simulate Call Declined
          </Button>
        </div>
      </div>

      <div className="p-4 bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Expected Behavior</h2>
        <ul className="text-gray-400 space-y-2">
          <li>• <strong>Incoming Call:</strong> Should open call window with "Incoming Call" title and Answer/Decline buttons</li>
          <li>• <strong>Call Accepted:</strong> Should transition call window to connected state</li>
          <li>• <strong>Call Declined:</strong> Should show declined message and close window after 2 seconds</li>
        </ul>
      </div>

      <div className="mt-6 p-4 bg-yellow-900 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">⚠️ Test Instructions</h2>
        <ol className="text-yellow-200 space-y-2">
          <li>1. Open browser console to see detailed logs</li>
          <li>2. Click "Simulate Incoming Call" to test the incoming call flow</li>
          <li>3. Check if call window opens with correct UI for incoming calls</li>
          <li>4. Test Answer and Decline buttons in the call window</li>
          <li>5. Use other buttons to test call state transitions</li>
        </ol>
      </div>
    </div>
  );
};
