import { invoke } from '@tauri-apps/api/core';
import React from 'react';

let cachedPlatform: string | null = null;

/**
 * Get the current platform (linux, windows, macos)
 */
export const getPlatform = async (): Promise<string> => {
  if (cachedPlatform) {
    return cachedPlatform;
  }

  try {
    cachedPlatform = await invoke<string>('get_platform');
    return cachedPlatform;
  } catch (error) {
    // Fallback to user agent detection
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();
    
    if (platform.includes('linux') || userAgent.includes('linux')) {
      cachedPlatform = 'linux';
    } else if (platform.includes('win') || userAgent.includes('windows')) {
      cachedPlatform = 'windows';
    } else if (platform.includes('mac') || userAgent.includes('darwin')) {
      cachedPlatform = 'macos';
    } else {
      cachedPlatform = 'unknown';
    }
    
    return cachedPlatform;
  }
};

/**
 * Check if current platform is Linux
 */
export const isLinux = async (): Promise<boolean> => {
  const platform = await getPlatform();
  return platform === 'linux';
};

/**
 * Get appropriate scrollbar CSS class based on platform
 */
export const getScrollbarClass = async (): Promise<string> => {
  const platform = await getPlatform();
  if (platform === 'linux') {
    return 'linux-hide-scrollbar';
  }
  return 'auto-hide-scrollbar';
};

/**
 * Hook for platform detection with React state
 */
export const usePlatform = () => {
  const [platform, setPlatform] = React.useState<string>('unknown');
  const [isLinuxPlatform, setIsLinuxPlatform] = React.useState<boolean>(false);
  
  React.useEffect(() => {
    getPlatform().then((p) => {
      setPlatform(p);
      setIsLinuxPlatform(p === 'linux');
    });
  }, []);
  
  return { platform, isLinux: isLinuxPlatform };
};