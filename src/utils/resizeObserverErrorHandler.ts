/**
 * ResizeObserver Error Handler
 * 
 * This utility helps prevent ResizeObserver errors that occur when
 * elements are removed from the DOM while being observed.
 */

// Store the original ResizeObserver
let OriginalResizeObserver: typeof ResizeObserver;

/**
 * Initialize ResizeObserver error handling
 * This should be called once at app startup
 */
export function initResizeObserverErrorHandler() {
  if (typeof window === 'undefined' || !window.ResizeObserver) {
    return;
  }

  // Store the original if not already stored
  if (!OriginalResizeObserver) {
    OriginalResizeObserver = window.ResizeObserver;
  }

  // Create a safe ResizeObserver class
  class SafeResizeObserver implements ResizeObserver {
    private observer: ResizeObserver;
    private observedElements = new WeakSet<Element>();

    constructor(callback: ResizeObserverCallback) {
      this.observer = new OriginalResizeObserver((entries, observer) => {
        try {
          // Filter out entries for elements that are no longer in the DOM
          const validEntries = entries.filter(entry => {
            return entry.target && document.contains(entry.target);
          });

          if (validEntries.length > 0) {
            callback(validEntries, observer);
          }
        } catch (error) {
          console.warn('ResizeObserver callback error:', error);
        }
      });
    }

    observe(target: Element, options?: ResizeObserverOptions): void {
      try {
        // Only observe if the element is in the DOM
        if (target && target instanceof Element && document.contains(target)) {
          this.observedElements.add(target);
          this.observer.observe(target, options);
        }
      } catch (error) {
        console.warn('ResizeObserver.observe error:', error);
      }
    }

    unobserve(target: Element): void {
      try {
        if (target && target instanceof Element) {
          this.observedElements.delete(target);
          this.observer.unobserve(target);
        }
      } catch (error) {
        console.warn('ResizeObserver.unobserve error:', error);
      }
    }

    disconnect(): void {
      try {
        this.observer.disconnect();
        // Clear the WeakSet (it will be garbage collected)
        this.observedElements = new WeakSet();
      } catch (error) {
        console.warn('ResizeObserver.disconnect error:', error);
      }
    }
  }

  // Replace the global ResizeObserver
  (window as any).ResizeObserver = SafeResizeObserver;

  // Also add a global error handler for uncaught ResizeObserver errors
  const errorHandler = (event: ErrorEvent) => {
    if (event.message && event.message.includes('ResizeObserver')) {
      console.warn('Caught ResizeObserver error:', event.message);
      event.preventDefault();
      return true;
    }
  };

  window.addEventListener('error', errorHandler);

  // Return cleanup function
  return () => {
    window.removeEventListener('error', errorHandler);
    if (OriginalResizeObserver) {
      (window as any).ResizeObserver = OriginalResizeObserver;
    }
  };
}

/**
 * Hook to use in React components that might trigger ResizeObserver errors
 */
export function useResizeObserverErrorHandler() {
  // This hook doesn't need to do anything since the global handler is already set up
  // But it can be used to ensure the component is aware of the error handling
}