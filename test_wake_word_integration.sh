#!/bin/bash

echo "🧪 Testing Wake Word Integration with TeamBy Desktop"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}🔵 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check prerequisites
print_step "Checking prerequisites..."

if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not found"
    exit 1
fi
print_success "Python 3 found: $(python3 --version)"

if ! command -v npm &> /dev/null; then
    print_error "npm is required but not found"
    exit 1
fi
print_success "npm found: $(npm --version)"

# Step 2: Check required files
print_step "Checking required files..."

required_files=(
    "openwakeword/team_bye_detector.py"
    "openwakeword/team_bye.tflite"
    "openwakeword/requirements.txt"
    "build_wake_word.py"
    "src-tauri/src/wake_word.rs"
    "src/components/WakeWordTest.tsx"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "Found: $file"
    else
        print_error "Missing: $file"
        exit 1
    fi
done

# Step 3: Build wake word detector
print_step "Building wake word detector..."
if python3 build_wake_word.py; then
    print_success "Wake word detector built successfully"
else
    print_error "Failed to build wake word detector"
    exit 1
fi

# Step 4: Test the built binary
print_step "Testing built binary..."
BINARY_PATH=""
if [ "$(uname)" = "Linux" ]; then
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-unknown-linux-gnu"
elif [ "$(uname)" = "Darwin" ]; then
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-apple-darwin"
else
    BINARY_PATH="src-tauri/binaries/team_bye_detector-x86_64-pc-windows-msvc.exe"
fi

if [ ! -f "$BINARY_PATH" ]; then
    print_error "Binary not found at: $BINARY_PATH"
    exit 1
fi

print_success "Binary found at: $BINARY_PATH"

# Make sure it's executable
chmod +x "$BINARY_PATH"

# Quick test (will show help and exit)
if "$BINARY_PATH" --help > /dev/null 2>&1; then
    print_success "Binary help command works"
else
    print_warning "Binary help command had issues, but proceeding..."
fi

# Step 5: Install npm dependencies
print_step "Installing npm dependencies..."
if npm install; then
    print_success "npm dependencies installed"
else
    print_error "Failed to install npm dependencies"
    exit 1
fi

# Step 6: Check Rust compilation
print_step "Checking Rust compilation..."
if cd src-tauri && cargo check && cd ..; then
    print_success "Rust compilation check passed"
else
    print_error "Rust compilation check failed"
    exit 1
fi

# Step 7: Quick dev test
print_step "Starting development test..."
print_warning "This will start the Tauri dev server for 30 seconds to test integration"
print_warning "Look for wake word detection messages in the console output"
print_warning "You can say 'Team Bye' to test detection during this time"

# Start Tauri dev in background
npm run tauri:dev &
DEV_PID=$!

# Wait for 30 seconds
echo "Waiting 30 seconds for testing... (PID: $DEV_PID)"
sleep 30

# Kill the dev process
kill $DEV_PID 2>/dev/null
wait $DEV_PID 2>/dev/null

print_success "Development test completed"

# Step 8: Clean up
print_step "Cleaning up..."
rm -rf build/ dist/ *.spec openwakeword_venv/ 2>/dev/null
print_success "Cleanup completed"

echo ""
echo "🎉 Integration test completed successfully!"
echo ""
echo "📋 Summary:"
echo "   ✅ Wake word detector binary built and tested"
echo "   ✅ Tauri integration configured"
echo "   ✅ Auto-start functionality added"
echo "   ✅ Event emission for debug logs implemented"
echo ""
echo "🚀 Next steps:"
echo "   1. Run 'npm run tauri:dev' for development testing"
echo "   2. Add WakeWordTest component to your UI for manual testing"
echo "   3. Run 'npm run tauri:build' for production build"
echo ""
echo "🎯 Usage:"
echo "   - Wake word detector starts automatically when Tauri starts"
echo "   - Say 'Team Bye' to trigger detection"
echo "   - Check console for debug logs and events"
echo "   - Use WakeWordTest component for manual control"
echo ""

# Show configuration summary
echo "⚙️  Configuration:"
echo "   - Threshold: 0.3 (lower = more sensitive)"
echo "   - Cooldown: 1.5 seconds"
echo "   - Model: openwakeword/team_bye.tflite"
echo "   - Binary: $BINARY_PATH"
echo ""